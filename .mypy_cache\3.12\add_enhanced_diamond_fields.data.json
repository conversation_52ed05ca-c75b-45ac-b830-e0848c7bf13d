{".class": "MypyFile", "_fullname": "add_enhanced_diamond_fields", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_enhanced_diamond_fields.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_enhanced_diamond_fields.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_enhanced_diamond_fields.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_enhanced_diamond_fields.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_enhanced_diamond_fields.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "add_enhanced_diamond_fields.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "branch_labels": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "add_enhanced_diamond_fields.branch_labels", "name": "branch_labels", "type": {".class": "NoneType"}}}, "depends_on": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "add_enhanced_diamond_fields.depends_on", "name": "depends_on", "type": {".class": "NoneType"}}}, "down_revision": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "add_enhanced_diamond_fields.down_revision", "name": "down_revision", "type": {".class": "NoneType"}}}, "downgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "add_enhanced_diamond_fields.downgrade", "name": "downgrade", "type": null}}, "op": {".class": "SymbolTableNode", "cross_ref": "alembic.op", "kind": "Gdef"}, "revision": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "add_enhanced_diamond_fields.revision", "name": "revision", "type": "builtins.str"}}, "sa": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy", "kind": "Gdef"}, "sqlite": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.sqlite", "kind": "Gdef"}, "upgrade": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "add_enhanced_diamond_fields.upgrade", "name": "upgrade", "type": null}}}, "path": "E:\\admin_panel\\admin_backend\\migrations\\versions\\add_enhanced_diamond_fields.py"}