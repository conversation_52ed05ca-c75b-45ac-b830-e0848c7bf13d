{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql.mariadbconnector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MySQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MySQLCompiler", "kind": "Gdef"}, "MySQLCompiler_mariadbconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.base.MySQLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLCompiler_mariadbconnector", "name": "MySQLCompiler_mariadbconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLCompiler_mariadbconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mariadbconnector", "mro": ["sqlalchemy.dialects.mysql.mariadbconnector.MySQLCompiler_mariadbconnector", "sqlalchemy.dialects.mysql.base.MySQLCompiler", "sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLCompiler_mariadbconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLCompiler_mariadbconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLDialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MySQLDialect", "kind": "Gdef"}, "MySQLDialect_mariadbconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.base.MySQLDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector", "name": "MySQLDialect_mariadbconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mariadbconnector", "mro": ["sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector", "sqlalchemy.dialects.mysql.base.MySQLDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.__init__", "name": "__init__", "type": null}}, "_dbapi_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector._dbapi_version", "name": "_dbapi_version", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector._dbapi_version", "name": "_dbapi_version", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_detect_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector._detect_charset", "name": "_detect_charset", "type": null}}, "_extract_error_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector._extract_error_code", "name": "_extract_error_code", "type": null}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "convert_unicode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.convert_unicode", "name": "convert_unicode", "type": "builtins.bool"}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.create_connect_args", "name": "create_connect_args", "type": null}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "do_begin_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.do_begin_twophase", "name": "do_begin_twophase", "type": null}}, "do_commit_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.do_commit_twophase", "name": "do_commit_twophase", "type": null}}, "do_prepare_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "xid"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.do_prepare_twophase", "name": "do_prepare_twophase", "type": null}}, "do_rollback_twophase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "connection", "xid", "is_prepared", "recover"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.do_rollback_twophase", "name": "do_rollback_twophase", "type": null}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.driver", "name": "driver", "type": "builtins.str"}}, "encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.encoding", "name": "encoding", "type": "builtins.str"}}, "execution_ctx_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.execution_ctx_cls", "name": "execution_ctx_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.get_isolation_level_values", "name": "get_isolation_level_values", "type": null}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.import_dbapi", "name": "import_dbapi", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of MySQLDialect_mariadbconnector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.is_disconnect", "name": "is_disconnect", "type": null}}, "set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.set_isolation_level", "name": "set_isolation_level", "type": null}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.statement_compiler", "name": "statement_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "_supporting_against", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "sqlalchemy.sql.compiler.<PERSON>", {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["sqlalchemy.dialects.mysql.mariadbconnector.MySQLCompiler_mariadbconnector"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLCompiler_mariadbconnector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_native_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.supports_native_decimal", "name": "supports_native_decimal", "type": "builtins.bool"}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": "builtins.bool"}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": "builtins.bool"}}, "supports_server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.supports_server_side_cursors", "name": "supports_server_side_cursors", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}, "supports_unicode_statements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.supports_unicode_statements", "name": "supports_unicode_statements", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "kind": "Gdef"}, "MySQLExecutionContext_mariadbconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.base.MySQLExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector", "name": "MySQLExecutionContext_mariadbconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mariadbconnector", "mro": ["sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector", "sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector._lastrowid", "name": "_lastrowid", "type": {".class": "NoneType"}}}, "create_default_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector.create_default_cursor", "name": "create_default_cursor", "type": null}}, "create_server_side_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector.create_server_side_cursor", "name": "create_server_side_cursor", "type": null}}, "get_lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector.get_lastrowid", "name": "get_lastrowid", "type": null}}, "post_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector.post_exec", "name": "post_exec", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLExecutionContext_mariadbconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MariaDBUUID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.sqltypes._UUID_RETURN", "id": 1, "name": "sqltypes._UUID_RETURN", "namespace": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID", "upper_bound": "builtins.object", "values": ["builtins.str", "uuid.UUID"], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.UUID"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID", "name": "_MariaDBUUID", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.sqltypes._UUID_RETURN", "id": 1, "name": "sqltypes._UUID_RETURN", "namespace": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID", "upper_bound": "builtins.object", "values": ["builtins.str", "uuid.UUID"], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mariadbconnector", "mro": ["sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID", "sqlalchemy.sql.sqltypes.UUID", "sqlalchemy.sql.sqltypes.Uuid", "sqlalchemy.sql.type_api.Emulated", "sqlalchemy.sql.type_api.NativeForEmulated", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.sql.sqltypes._UUID_RETURN", "id": 1, "name": "sqltypes._UUID_RETURN", "namespace": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID", "upper_bound": "builtins.object", "values": ["builtins.str", "uuid.UUID"], "variance": 0}], "extra_attrs": null, "type_ref": "sqlalchemy.dialects.mysql.mariadbconnector._MariaDBUUID"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["sqltypes._UUID_RETURN"], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_python_UUID": {".class": "SymbolTableNode", "cross_ref": "uuid.UUID", "kind": "Gdef"}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.dialect", "line": 277, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.mariadbconnector.MySQLDialect_mariadbconnector"}}, "mariadb_cpy_minimum_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mariadbconnector.mariadb_cpy_minimum_version", "name": "mariadb_cpy_minimum_version", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mariadbconnector.py"}