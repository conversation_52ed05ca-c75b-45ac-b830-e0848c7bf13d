{"data_mtime": 1753035443, "dep_lines": [28, 29, 34, 35, 40, 42, 45, 47, 49, 27, 32, 33, 43, 46, 4, 6, 8, 9, 10, 24, 30, 32, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 5, 25, 25, 25, 25, 25, 5, 10, 10, 25, 25, 5, 5, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.url", "sqlalchemy.engine.strategies", "alembic.util.sqla_compat", "alembic.util.compat", "sqlalchemy.engine.base", "sqlalchemy.engine.mock", "alembic.runtime.environment", "alembic.script.base", "alembic.script.revision", "sqlalchemy.engine", "alembic.ddl", "alembic.util", "sqlalchemy.sql", "alembic.config", "__future__", "contextlib", "logging", "sys", "typing", "sqlalchemy", "typing_extensions", "alembic", "builtins", "_frozen_importlib", "_io", "abc", "alembic.ddl.impl", "alembic.script", "alembic.util.langhelpers", "io", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "4f38134b958e12ae9056cf751aaef1183d948c1d", "id": "alembic.runtime.migration", "ignore_all": true, "interface_hash": "2aef21682e812da6a98dab0f9d4fb32e4b18ee4c", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\runtime\\migration.py", "plugin_data": null, "size": 49997, "suppressed": [], "version_id": "1.15.0"}