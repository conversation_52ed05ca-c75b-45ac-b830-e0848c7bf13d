import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { 
  Diamond, 
  Gem, 
  ShoppingCart, 
  Wrench, 
  TrendingUp,
  Package,
  DollarSign
} from 'lucide-react';
import { api } from '../lib/api';
import { DashboardSummary, SalesStats, StockLevels, DashboardActivity } from '../types';
import Card from '../components/ui/Card';
import { LoadingState, ErrorState } from '../components/DataStates';
import { format } from 'date-fns';

const Dashboard: React.FC = () => {
  const { 
    data: summary, 
    isLoading: summaryLoading, 
    error: summaryError,
    refetch: refetchSummary 
  } = useQuery({
    queryKey: ['dashboard-summary'],
    queryFn: async (): Promise<DashboardSummary> => {
      const response = await api.dashboard.summary();
      if (!response) {
        throw new Error('Failed to fetch dashboard summary');
      }
      return response;
    },
    retry: 1
  });

  const { 
    data: salesStats, 
    isLoading: salesLoading, 
    error: salesError,
    refetch: refetchSales 
  } = useQuery({
    queryKey: ['dashboard-sales'],
    queryFn: async (): Promise<SalesStats> => {
      const response = await api.dashboard.sales();
      if (!response) {
        throw new Error('Failed to fetch sales stats');
      }
      return response;
    },
    retry: 1
  });

  const { 
    data: stockLevels, 
    isLoading: stockLoading, 
    error: stockError,
    refetch: refetchStock 
  } = useQuery({
    queryKey: ['dashboard-stock'],
    queryFn: async (): Promise<StockLevels> => {
      const response = await api.dashboard.stock();
      if (!response) {
        throw new Error('Failed to fetch stock levels');
      }
      return response;
    },
    retry: 1
  });

  const {
    data: activity,
    isLoading: activityLoading,
    error: activityError,
    refetch: refetchActivity
  } = useQuery({
    queryKey: ['dashboard-activity'],
    queryFn: async (): Promise<DashboardActivity[]> => {
      try {
        const response = await api.dashboard.activity();
        if (!response) {
          throw new Error('Failed to fetch recent activity');
        }
        return Array.isArray(response) ? response : [];
      } catch (error) {
        console.warn('Activity endpoint not available, returning empty array');
        return [];
      }
    },
    retry: 1
  });

  const isLoading = summaryLoading || salesLoading || stockLoading || activityLoading;
  const hasError = summaryError || salesError || stockError || activityError;

  // Enhanced error handling for API calls
  const handleRetry = () => {
    refetchSummary();
    refetchSales();
    refetchStock();
    refetchActivity();
  };

  if (isLoading) {
    return <div className="spinner">Loading dashboard...</div>;
  }

  if (hasError) {
    const errorDetails = {
      summary: summaryError?.message || 'Summary data unavailable',
      sales: salesError?.message || 'Sales data unavailable',
      stock: stockError?.message || 'Stock data unavailable',
    };

    return (
      <ErrorState
        title="Failed to load dashboard"
        description={`Unable to load dashboard data. Details: ${JSON.stringify(errorDetails)}`}
        onRetry={handleRetry}
        error={(summaryError || salesError || stockError) as Error | undefined}
      />
    );
  }

  // Refactored reusable card rendering logic
  const renderCards = (cards: Array<{
    title: string;
    value: number;
    icon: React.ElementType;
    color: string;
    change?: string;
  }>) => (
    cards.map((card, index) => (
      <Card key={index} className="relative overflow-hidden">
        <div className="flex items-center">
          <div className="flex-1">
            <p className="text-sm font-medium text-gray-600">{card.title}</p>
            <p className="text-2xl font-bold text-gray-900">{card.value.toLocaleString()}</p>
            {card.change && (
              <p className={`text-sm font-medium ${
                card.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
              }`}>
                {card.change} from last month
              </p>
            )}
          </div>
          <div className={`p-3 rounded-full ${card.color}`}>
            {card.icon ? (
              <card.icon className="h-6 w-6 text-white" />
            ) : (
              <div className="h-6 w-6 bg-gray-300 rounded-full flex items-center justify-center">
                <span className="text-xs text-gray-500">No Icon</span>
              </div>
            )}
          </div>
        </div>
      </Card>
    ))
  );

  const summaryCards = [
    {
      title: 'Diamonds in Stock',
      value: summary?.diamonds_in_stock || 0,
      icon: Diamond,
      color: 'bg-blue-500',
    },
    {
      title: 'Jewelry in Stock',
      value: summary?.jewelry_in_stock || 0,
      icon: Gem,
      color: 'bg-emerald-500',
    },
    {
      title: 'Total Sales',
      value: summary?.total_sales || 0,
      icon: ShoppingCart,
      color: 'bg-amber-500',
    },
    {
      title: 'Open Manufacturing',
      value: summary?.open_manufacturing || 0,
      icon: Wrench,
      color: 'bg-purple-500',
    }
  ];

  const salesCards = [
    {
      title: 'Total Sales',
      value: salesStats?.total_sales || 0,
      icon: TrendingUp,
      color: 'bg-green-500'
    },
    {
      title: 'Paid Sales',
      value: salesStats?.paid_sales || 0,
      icon: DollarSign,
      color: 'bg-blue-500'
    },
    {
      title: 'Unpaid Sales',
      value: salesStats?.unpaid_sales || 0,
      icon: Package,
      color: 'bg-red-500'
    }
  ];

  const formattedDate = format(new Date(), 'dd MMM yyyy');

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-gray-600">Welcome to your inventory management system</p>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {renderCards(summaryCards)}
      </div>

      {/* Sales Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales Overview</h3>
          <div className="space-y-4">
            {renderCards(salesCards)}
          </div>
        </Card>

        <Card>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Stock Summary</h3>
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Diamond className="h-5 w-5 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-gray-700">Diamonds</span>
              </div>
              <span className="text-lg font-bold text-gray-900">
                {stockLevels?.diamonds_in_stock || 0}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Gem className="h-5 w-5 text-emerald-600 mr-2" />
                <span className="text-sm font-medium text-gray-700">Jewelry Items</span>
              </div>
              <span className="text-lg font-bold text-gray-900">
                {stockLevels?.jewelry_in_stock || 0}
              </span>
            </div>
            <div className="pt-4 border-t border-gray-200">
              <div className="text-center">
                <p className="text-sm text-gray-600">Total Inventory Value</p>
                <p className="text-2xl font-bold text-gray-900">
                  {summary?.total_inventory_value?.toLocaleString('en-IN', { style: 'currency', currency: 'INR' }) ?? '₹0'}
                </p>
              </div>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-2">
          {activity?.map((item, idx) => (
            <div key={idx} className="text-sm text-gray-700">
              <strong>{item.type}:</strong> {item.description} — <em>{new Date(item.date).toLocaleString()}</em>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default Dashboard;