import React from 'react';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useManagementState } from '../../hooks/useManagementState';
import toast from 'react-hot-toast';

const ManageManufacturing: React.FC = () => {
  const {
    items: manufacturing,
    isLoading,
    error,
    newItem: newManufacturing,
    setNewItem: setNewManufacturing,
    addItemMutation: addManufacturingMutation,
    deleteItemMutation: deleteManufacturingMutation,  } = useManagementState({
    queryKey: 'manufacturing',
    fetchUrl: '/diamonds/manufacturing-types',
    createUrl: '/diamonds/manufacturing-types',
    deleteUrl: '/diamonds/manufacturing-types',
  });

  const handleAddManufacturing = () => {
    if (!newManufacturing.trim()) {
      toast.error('Manufacturing name cannot be empty');
      return;
    }
    if (newManufacturing.length > 100) {
      toast.error('Manufacturing name must be less than 100 characters');
      return;
    }
    addManufacturingMutation.mutate(newManufacturing);
  };

  const handleDeleteManufacturing = (manufacturingId: number) => {
    if (window.confirm('Are you sure you want to delete this manufacturing process?')) {
      deleteManufacturingMutation.mutate(manufacturingId);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Manage Manufacturing Processes</h1>
      <Card>
        <div className="space-y-4">
          <Input
            label="New Manufacturing Process"
            value={newManufacturing}
            onChange={(e) => setNewManufacturing(e.target.value)}
            placeholder="Enter manufacturing name"
          />
          <Button onClick={handleAddManufacturing} isLoading={addManufacturingMutation.isPending}>
            Add Manufacturing
          </Button>
        </div>
      </Card>
      <div className="space-y-4">
        {isLoading && <p>Loading manufacturing processes...</p>}
        {error && <p>Error loading manufacturing processes</p>}
        {manufacturing?.map((process: { id: number; name: string }) => (
          <div key={process.id} className="flex justify-between items-center">
            <span>{process.name}</span>
            <Button onClick={() => handleDeleteManufacturing(process.id)} isLoading={deleteManufacturingMutation.isPending}>
              Delete
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ManageManufacturing;
