{"data_mtime": 1753035423, "dep_lines": [1, 2, 3, 4, 5, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 30, 30], "dependencies": ["abc", "itertools", "os", "pathlib", "typing", "builtins", "_frozen_importlib", "types"], "hash": "ed862f9ab781a9400b9b1745b7dab467f62d2912", "id": "importlib_resources.abc", "ignore_all": true, "interface_hash": "54de6230025a632ea6d9dd5c959381edd9c566a4", "mtime": 1750655020, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\importlib_resources\\abc.py", "plugin_data": null, "size": 5549, "suppressed": [], "version_id": "1.15.0"}