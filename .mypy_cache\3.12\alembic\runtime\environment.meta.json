{"data_mtime": 1753035443, "dep_lines": [18, 23, 27, 31, 35, 38, 41, 42, 25, 26, 30, 32, 39, 40, 1, 3, 20, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 25, 25, 10, 5, 25, 25, 25, 25, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.schema", "alembic.runtime.migration", "alembic.script.revision", "sqlalchemy.engine.base", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.operations.ops", "alembic.script.base", "alembic.util", "alembic.operations", "sqlalchemy.engine", "sqlalchemy.sql", "alembic.config", "alembic.ddl", "__future__", "typing", "typing_extensions", "alembic", "builtins", "_frozen_importlib", "_typeshed", "abc", "alembic.autogenerate", "alembic.ddl.impl", "alembic.script", "alembic.util.langhelpers", "contextlib", "sqlalchemy", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "461812df072ca0bdf1896f6193ecadcc52cd5eed", "id": "alembic.runtime.environment", "ignore_all": true, "interface_hash": "2720bbe5736304a0fd488250c53cbb220595bda8", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\runtime\\environment.py", "plugin_data": null, "size": 41479, "suppressed": [], "version_id": "1.15.0"}