{".class": "MypyFile", "_fullname": "alembic.autogenerate.render", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ARRAY": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.ARRAY", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutogenContext": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.AutogenContext", "kind": "Gdef"}, "CheckConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.CheckConstraint", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "Computed": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Computed", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "alembic.config.Config", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FetchedValue": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.FetchedValue", "kind": "Gdef"}, "ForeignKey": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKey", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "Identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Identity", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "Label": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.Label", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MAX_PYTHON_ARGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.render.MAX_PYTHON_ARGS", "name": "MAX_PYTHON_ARGS", "type": "builtins.int"}}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "MigrationScript": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrationScript", "kind": "Gdef"}, "ModifyTableOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.ModifyTableOps", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PrimaryKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.PrimaryKeyConstraint", "kind": "Gdef"}, "PythonPrinter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render.PythonPrinter", "name": "PythonPrinter", "type": {".class": "AnyType", "missing_import_name": "alembic.autogenerate.render.PythonPrinter", "source_any": null, "type_of_any": 3}}}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "_DialectArgView": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._DialectArgView", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.render.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.render.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.render.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.render.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.render.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.render.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_add_check_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["constraint", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._add_check_constraint", "name": "_add_check_constraint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._add_check_constraint", "name": "_add_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["constraint", "autogen_context"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_check_constraint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_add_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._add_column", "name": "_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AddColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._add_column", "name": "_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AddColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_add_fk_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._add_fk_constraint", "name": "_add_fk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateForeignKeyOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_fk_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._add_fk_constraint", "name": "_add_fk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateForeignKeyOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_fk_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_add_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._add_index", "name": "_add_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateIndexOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_index", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._add_index", "name": "_add_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateIndexOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_index", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_add_pk_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["constraint", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._add_pk_constraint", "name": "_add_pk_constraint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._add_pk_constraint", "name": "_add_pk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["constraint", "autogen_context"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_pk_constraint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_add_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._add_table", "name": "_add_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateTableOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._add_table", "name": "_add_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateTableOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_add_unique_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._add_unique_constraint", "name": "_add_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateUniqueConstraintOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_unique_constraint", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._add_unique_constraint", "name": "_add_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateUniqueConstraintOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_unique_constraint", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_alembic_autogenerate_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._alembic_autogenerate_prefix", "name": "_alembic_autogenerate_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["autogen_context"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alembic_autogenerate_prefix", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_alter_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._alter_column", "name": "_alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alter_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._alter_column", "name": "_alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_alter_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_constraint_renderers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.render._constraint_renderers", "name": "_constraint_renderers", "type": "alembic.util.langhelpers.Dispatcher"}}, "_drop_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._drop_column", "name": "_drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._drop_column", "name": "_drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropColumnOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_drop_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._drop_constraint", "name": "_drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropConstraintOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._drop_constraint", "name": "_drop_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropConstraintOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_drop_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._drop_index", "name": "_drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropIndexOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_index", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._drop_index", "name": "_drop_index", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropIndexOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_index", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_drop_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._drop_table", "name": "_drop_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropTableOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._drop_table", "name": "_drop_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropTableOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_drop_table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_execute_sql": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._execute_sql", "name": "_execute_sql", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ExecuteSQLOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_sql", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._execute_sql", "name": "_execute_sql", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ExecuteSQLOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_execute_sql", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_f_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.render._f_name", "name": "_f_name", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._f_name", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.render", "mro": ["alembic.autogenerate.render._f_name", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._f_name.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "prefix", "name"], "arg_types": ["alembic.autogenerate.render._f_name", "builtins.str", "sqlalchemy.sql.elements.conv"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _f_name", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._f_name.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["alembic.autogenerate.render._f_name"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of _f_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.render._f_name.name", "name": "name", "type": "sqlalchemy.sql.elements.conv"}}, "prefix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.render._f_name.prefix", "name": "prefix", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.render._f_name.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.render._f_name", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_fk_colspec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["fk", "metadata_schema", "namespace_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._fk_colspec", "name": "_fk_colspec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["fk", "metadata_schema", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.ForeignKey", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fk_colspec", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_index_rendered_expressions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["idx", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._get_index_rendered_expressions", "name": "_get_index_rendered_expressions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["idx", "autogen_context"], "arg_types": ["sqlalchemy.sql.schema.Index", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_index_rendered_expressions", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ident": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._ident", "name": "_ident", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ident", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_indent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._indent", "name": "_indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_indent", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_populate_render_fk_opts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["constraint", "opts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._populate_render_fk_opts", "name": "_populate_render_fk_opts", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["constraint", "opts"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_populate_render_fk_opts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_ARRAY_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["type_", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_ARRAY_type", "name": "_render_ARRAY_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["type_", "autogen_context"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.sqltypes.ARRAY"}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_ARRAY_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_Variant_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["type_", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_Variant_type", "name": "_render_Variant_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["type_", "autogen_context"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_Variant_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_check_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._render_check_constraint", "name": "_render_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint", "alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_check_constraint", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._render_check_constraint", "name": "_render_check_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.CheckConstraint", "alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_check_constraint", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_cmd_body": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["op_container", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_cmd_body", "name": "_render_cmd_body", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["op_container", "autogen_context"], "arg_types": ["alembic.operations.ops.OpContainer", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_cmd_body", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["column", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_column", "name": "_render_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["column", "autogen_context"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_computed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["computed", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_computed", "name": "_render_computed", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["computed", "autogen_context"], "arg_types": ["sqlalchemy.sql.schema.Computed", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_computed", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_constraint", "name": "_render_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.Constraint", "alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_constraint", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_create_table_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._render_create_table_comment", "name": "_render_create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_create_table_comment", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._render_create_table_comment", "name": "_render_create_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.CreateTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_create_table_comment", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_dialect_kwargs_items": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "dialect_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_dialect_kwargs_items", "name": "_render_dialect_kwargs_items", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "dialect_kwargs"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "sqlalchemy.sql.base._DialectArgView"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_dialect_kwargs_items", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_drop_table_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._render_drop_table_comment", "name": "_render_drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_drop_table_comment", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._render_drop_table_comment", "name": "_render_drop_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.DropTableCommentOp"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_drop_table_comment", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_foreign_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._render_foreign_key", "name": "_render_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", "alembic.autogenerate.api.AutogenContext", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_foreign_key", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._render_foreign_key", "name": "_render_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint", "alembic.autogenerate.api.AutogenContext", "sqlalchemy.sql.schema.MetaData"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_foreign_key", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_gen_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_gen_name", "name": "_render_gen_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "name"], "arg_types": ["alembic.autogenerate.api.AutogenContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_gen_name", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str", "alembic.autogenerate.render._f_name", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["identity", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_identity", "name": "_render_identity", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["identity", "autogen_context"], "arg_types": ["sqlalchemy.sql.schema.Identity", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_identity", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_modify_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._render_modify_table", "name": "_render_modify_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_modify_table", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._render_modify_table", "name": "_render_modify_table", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_modify_table", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_potential_expr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["value", "autogen_context", "wrap_in_element", "is_server_default", "is_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_potential_expr", "name": "_render_potential_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["value", "autogen_context", "wrap_in_element", "is_server_default", "is_index"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "alembic.autogenerate.api.AutogenContext", "builtins.bool", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_potential_expr", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_primary_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._render_primary_key", "name": "_render_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint", "alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_primary_key", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._render_primary_key", "name": "_render_primary_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.PrimaryKeyConstraint", "alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_primary_key", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_render_python_into_templatevars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["autogen_context", "migration_script", "template_args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_python_into_templatevars", "name": "_render_python_into_templatevars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["autogen_context", "migration_script", "template_args"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.MigrationScript", {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "alembic.config.Config"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_python_into_templatevars", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_server_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["default", "autogen_context", "repr_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_server_default", "name": "_render_server_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["default", "autogen_context", "repr_"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.schema.FetchedValue", "builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "alembic.autogenerate.api.AutogenContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_server_default", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_type_w_subtype": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["type_", "autogen_context", "attrname", "regexp", "prefix"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._render_type_w_subtype", "name": "_render_type_w_subtype", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["type_", "autogen_context", "attrname", "regexp", "prefix"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "alembic.autogenerate.api.AutogenContext", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_type_w_subtype", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_unique_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.render._render_unique_constraint", "name": "_render_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.UniqueConstraint", "alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_unique_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.render._render_unique_constraint", "name": "_render_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "namespace_metadata"], "arg_types": ["sqlalchemy.sql.schema.UniqueConstraint", "alembic.autogenerate.api.AutogenContext", {".class": "UnionType", "items": ["sqlalchemy.sql.schema.MetaData", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_unique_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_repr_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["type_", "autogen_context", "_skip_variants"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._repr_type", "name": "_repr_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["type_", "autogen_context", "_skip_variants"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, "alembic.autogenerate.api.AutogenContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_should_render_server_default_positionally": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["server_default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._should_render_server_default_positionally", "name": "_should_render_server_default_positionally", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["server_default"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_should_render_server_default_positionally", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sqlalchemy_autogenerate_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._sqlalchemy_autogenerate_prefix", "name": "_sqlalchemy_autogenerate_prefix", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["autogen_context"], "arg_types": ["alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sqlalchemy_autogenerate_prefix", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_uq_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "alter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._uq_constraint", "name": "_uq_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["constraint", "autogen_context", "alter"], "arg_types": ["sqlalchemy.sql.schema.UniqueConstraint", "alembic.autogenerate.api.AutogenContext", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_uq_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_user_autogenerate_prefix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._user_autogenerate_prefix", "name": "_user_autogenerate_prefix", "type": null}}, "_user_defined_render": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["type_", "object_", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render._user_defined_render", "name": "_user_defined_render", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["type_", "object_", "autogen_context"], "arg_types": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_user_defined_render", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "conv": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.conv", "kind": "Gdef"}, "default_renderers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.render.default_renderers", "name": "default_renderers", "type": "alembic.util.langhelpers.Dispatcher"}}, "ops": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "render_op": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render.render_op", "name": "render_op", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_op", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "render_op_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.render.render_op_text", "name": "render_op_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "op"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "render_op_text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "renderers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.render.renderers", "name": "renderers", "type": "alembic.util.langhelpers.Dispatcher"}}, "sa_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\autogenerate\\render.py"}