import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, LoginRequest } from '../types';
import { api, TokenManager } from '../lib/api';
import toast from 'react-hot-toast';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const isAuthenticated = !!user;

  useEffect(() => {
    const initAuth = async () => {
      const token = TokenManager.getAccessToken();
      
      if (token) {
        try {
          const response = await api.auth.me();
          if (response?.data) {
            // Extract user from response.data.user (not response.data)
            const userData = response.data.user || response.data;
            // Check if user is admin
            if (userData.role !== 'admin') {
              TokenManager.clearTokens();
              toast.error('Access denied. Admin privileges required.');
              setIsLoading(false);
              return;
            }
            setUser(userData);
          }
        } catch (error) {
          TokenManager.clearTokens();
        }
      }
      setIsLoading(false);
    };

    initAuth();
  }, []);

  const login = async (credentials: LoginRequest) => {
    try {
      setIsLoading(true);
      const response = await api.auth.login(credentials);
      
      if (response?.data) {
        const { access_token, refresh_token, user: userData } = response.data;

        // Check if user is admin
        if (userData.role !== 'admin') {
          toast.error('Access denied. Admin privileges required.');
          return;
        }

        TokenManager.setTokens(access_token, refresh_token);
        setUser(userData);
        toast.success('Login successful!');
      }
    } catch (error: any) {
      const message = error.response?.data?.message || error.message || 'Login failed';
      toast.error(message);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async () => {
    try {
      await api.auth.logout();
    } catch (error) {
      // Ignore logout errors
    } finally {
      TokenManager.clearTokens();
      setUser(null);
      toast.success('Logged out successfully');
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      isLoading,
      isAuthenticated,
      login,
      logout,
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};