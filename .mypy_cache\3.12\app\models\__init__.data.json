{".class": "MypyFile", "_fullname": "app.models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Diamond": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Diamond", "kind": "Gdef"}, "JewelryItem": {".class": "SymbolTableNode", "cross_ref": "app.models.jewelry.JewelryItem", "kind": "Gdef"}, "ManufacturingRequest": {".class": "SymbolTableNode", "cross_ref": "app.models.manufacturing.ManufacturingRequest", "kind": "Gdef"}, "Role": {".class": "SymbolTableNode", "cross_ref": "app.models.role.Role", "kind": "Gdef"}, "Sale": {".class": "SymbolTableNode", "cross_ref": "app.models.sale.Sale", "kind": "Gdef"}, "TokenBlacklist": {".class": "SymbolTableNode", "cross_ref": "app.models.token.TokenBlacklist", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "app.models.user.User", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "jewelry_diamonds": {".class": "SymbolTableNode", "cross_ref": "app.models.jewelry.jewelry_diamonds", "kind": "Gdef"}, "manufacturing_diamonds": {".class": "SymbolTableNode", "cross_ref": "app.models.manufacturing.manufacturing_diamonds", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\models\\__init__.py"}