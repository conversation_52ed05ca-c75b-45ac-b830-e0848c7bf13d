{"data_mtime": 1753035424, "dep_lines": [13, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30], "dependencies": ["importlib_resources.abc", "contextlib", "functools", "importlib", "inspect", "itertools", "os", "pathlib", "tempfile", "types", "warnings", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "92af0b647c32c4e1291b7aa1ce6d382b45fcac8d", "id": "importlib_resources._common", "ignore_all": true, "interface_hash": "5c98f9e680a2104504092d2671c859cdc9a5d5ce", "mtime": 1750655020, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\importlib_resources\\_common.py", "plugin_data": null, "size": 5624, "suppressed": [], "version_id": "1.15.0"}