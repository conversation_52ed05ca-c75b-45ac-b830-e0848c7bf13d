{".class": "MypyFile", "_fullname": "importlib_resources", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Anchor": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._common.Anchor", "kind": "Gdef"}, "Package": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._common.Package", "kind": "Gdef"}, "ResourceReader": {".class": "SymbolTableNode", "cross_ref": "importlib_resources.abc.ResourceReader", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "importlib_resources.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib_resources.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib_resources.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib_resources.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib_resources.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib_resources.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib_resources.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "importlib_resources.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "as_file": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._common.as_file", "kind": "Gdef"}, "contents": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._functional.contents", "kind": "Gdef"}, "files": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._common.files", "kind": "Gdef"}, "is_resource": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._functional.is_resource", "kind": "Gdef"}, "open_binary": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._functional.open_binary", "kind": "Gdef"}, "open_text": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._functional.open_text", "kind": "Gdef"}, "path": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._functional.path", "kind": "Gdef"}, "read_binary": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._functional.read_binary", "kind": "Gdef"}, "read_text": {".class": "SymbolTableNode", "cross_ref": "importlib_resources._functional.read_text", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\importlib_resources\\__init__.py"}