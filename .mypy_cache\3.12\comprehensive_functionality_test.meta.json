{"data_mtime": 1753039250, "dep_lines": [7, 8, 9, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "json", "sys", "time", "datetime", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "http", "http.cookiejar", "requests.auth", "requests.models", "requests.sessions", "types", "typing"], "hash": "324cf91d5b153e64bc11015d0ee681f3c2756255", "id": "comprehensive_functionality_test", "ignore_all": false, "interface_hash": "b7bfed11cf202909bf2863fd9f112515c5145c7d", "mtime": 1753039246, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\comprehensive_functionality_test.py", "plugin_data": null, "size": 25694, "suppressed": [], "version_id": "1.15.0"}