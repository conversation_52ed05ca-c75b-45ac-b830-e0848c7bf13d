{"data_mtime": 1753088442, "dep_lines": [29, 29, 29, 29, 29, 30, 25, 27, 29, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["PIL.Image", "PIL.ImageColor", "PIL.ImageDraw", "PIL.ImageFont", "PIL.ImagePath", "PIL._typing", "__future__", "typing", "PIL", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "functools", "os", "types"], "hash": "f957db79ebe7337fbe325fbe82a89be28e8bedf7", "id": "PIL.ImageDraw2", "ignore_all": true, "interface_hash": "5899acad99e4474d1e924d79414e23882d1ca1e7", "mtime": 1750655007, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\PIL\\ImageDraw2.py", "plugin_data": null, "size": 7470, "suppressed": [], "version_id": "1.15.0"}