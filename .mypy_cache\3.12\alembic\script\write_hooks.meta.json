{"data_mtime": 1753035443, "dep_lines": [19, 20, 18, 23, 4, 6, 7, 8, 9, 10, 18, 1, 1, 1], "dep_prios": [10, 5, 10, 25, 5, 10, 10, 10, 10, 5, 20, 5, 30, 30], "dependencies": ["alembic.util.compat", "alembic.util.pyfiles", "alembic.util", "alembic.config", "__future__", "os", "shlex", "subprocess", "sys", "typing", "alembic", "builtins", "_frozen_importlib", "abc"], "hash": "3b553305c8600594656422822cdf01cb7366318e", "id": "alembic.script.write_hooks", "ignore_all": true, "interface_hash": "769eaf193eff419e722f1f4200c0926859f75b5e", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\script\\write_hooks.py", "plugin_data": null, "size": 5015, "suppressed": [], "version_id": "1.15.0"}