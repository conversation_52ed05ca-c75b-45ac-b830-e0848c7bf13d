{"data_mtime": 1753074105, "dep_lines": [3, 4, 6, 10, 2, 5, 7, 8, 165, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 9], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["app.models.sale", "app.models.jewelry", "app.utils.decorators", "sqlalchemy.exc", "flask", "app", "datetime", "io", "time", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "app.models", "app.utils", "flask.globals", "flask.wrappers", "flask_sqlalchemy", "flask_sqlalchemy.extension", "sqlalchemy", "typing", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.wrappers", "werkzeug.wrappers.request"], "hash": "2c6f05e4645304a5ec0cad96eb1af1f4fe9683f8", "id": "app.api.sale", "ignore_all": true, "interface_hash": "5c59ab78ee9bceff7e2cccc2c27dd58f64fe29ab", "mtime": 1753042801, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\sale.py", "plugin_data": null, "size": 12948, "suppressed": ["flask_restx", "xhtml2pdf"], "version_id": "1.15.0"}