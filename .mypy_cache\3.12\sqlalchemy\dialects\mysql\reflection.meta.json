{"data_mtime": 1753035435, "dep_lines": [12, 14, 17, 18, 19, 10, 17, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mysql.enumerated", "sqlalchemy.dialects.mysql.types", "sqlalchemy.log", "sqlalchemy.types", "sqlalchemy.util", "re", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "typing"], "hash": "4fe19f32bf62dfa6db909dd7281619917dd95af8", "id": "sqlalchemy.dialects.mysql.reflection", "ignore_all": true, "interface_hash": "9d6f800f8460eec825c4ab1a1aaf98a72fa04dc3", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py", "plugin_data": null, "size": 23519, "suppressed": [], "version_id": "1.15.0"}