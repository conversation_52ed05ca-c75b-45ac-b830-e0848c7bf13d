{".class": "MypyFile", "_fullname": "app.api.diamond", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AssignJewelry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.AssignJewelry", "name": "As<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.AssignJewelry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.AssignJewelry", "builtins.object"], "names": {".class": "SymbolTable", "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "diamond_id", "jewelry_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.AssignJewelry.patch", "name": "patch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.AssignJewelry.patch", "name": "patch", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.AssignJewelry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.AssignJewelry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AssignManufacturing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.AssignManufacturing", "name": "AssignManufacturing", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.AssignManufacturing", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.AssignManufacturing", "builtins.object"], "names": {".class": "SymbolTable", "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "diamond_id", "manufacturing_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.AssignManufacturing.patch", "name": "patch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.AssignManufacturing.patch", "name": "patch", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.AssignManufacturing.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.AssignManufacturing", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Diamond": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Diamond", "kind": "Gdef"}, "DiamondBulkOperations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.DiamondBulkOperations", "name": "DiamondBulkOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.DiamondBulkOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.DiamondBulkOperations", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondBulkOperations.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondBulkOperations.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondBulkOperations.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondBulkOperations.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondBulkOperations.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondBulkOperations.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.DiamondBulkOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.DiamondBulkOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DiamondCRUD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.DiamondCRUD", "name": "DiamondCRUD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.DiamondCRUD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.DiamondCRUD", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "diamond_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondCRUD.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondCRUD.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "diamond_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondCRUD.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondCRUD.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "diamond_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondCRUD.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondCRUD.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.DiamondCRUD.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.DiamondCRUD", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DiamondDeduct": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.DiamondDeduct", "name": "DiamondDeduct", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.DiamondDeduct", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.DiamondDeduct", "builtins.object"], "names": {".class": "SymbolTable", "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "diamond_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondDeduct.patch", "name": "patch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondDeduct.patch", "name": "patch", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.DiamondDeduct.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.DiamondDeduct", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DiamondExport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.DiamondExport", "name": "DiamondExport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.DiamondExport", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.DiamondExport", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondExport.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondExport.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.DiamondExport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.DiamondExport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DiamondList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.DiamondList", "name": "DiamondList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.DiamondList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.DiamondList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.DiamondList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.DiamondList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.DiamondList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.DiamondList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc.IntegrityError", "kind": "Gdef"}, "Jewelry": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Jewelry", "kind": "Gdef"}, "Manufacturing": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Manufacturing", "kind": "Gdef"}, "ManufacturingTypesList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.ManufacturingTypesList", "name": "ManufacturingTypesList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.ManufacturingTypesList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.ManufacturingTypesList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.ManufacturingTypesList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.ManufacturingTypesList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.ManufacturingTypesList.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.ManufacturingTypesList.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.ManufacturingTypesList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.ManufacturingTypesList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NamedTemporaryFile": {".class": "SymbolTableNode", "cross_ref": "tempfile.NamedTemporaryFile", "kind": "Gdef"}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.diamond.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.diamond.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Resource", "source_any": null, "type_of_any": 3}}}, "Shape": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Shape", "kind": "Gdef"}, "ShapeCRUD": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.ShapeCRUD", "name": "ShapeCRUD", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.ShapeCRUD", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.ShapeCRUD", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.ShapeCRUD.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.ShapeCRUD.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "shape_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.ShapeCRUD.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.ShapeCRUD.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.ShapeCRUD.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.ShapeCRUD", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ShapeList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond.ShapeList", "name": "ShapeList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond.ShapeList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond", "mro": ["app.api.diamond.ShapeList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.ShapeList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.ShapeList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond.ShapeList.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond.ShapeList.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond.ShapeList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond.ShapeList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Vendor": {".class": "SymbolTableNode", "cross_ref": "app.models.vendor.Vendor", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "admin_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.admin_required", "kind": "Gdef"}, "csv": {".class": "SymbolTableNode", "cross_ref": "csv", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "diamond_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.diamond_model", "name": "diamond_model", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "diamond_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.diamond_ns", "name": "diamond_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "diamond_to_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["d"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.diamond.diamond_to_dict", "name": "diamond_to_dict", "type": null}}, "diamond_update_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.diamond_update_model", "name": "diamond_update_model", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.diamond.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.fields", "source_any": null, "type_of_any": 3}}}, "formatter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.formatter", "name": "formatter", "type": "logging.Formatter"}}, "handle_errors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.diamond.handle_errors", "name": "handle_errors", "type": null}}, "handler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.handler", "name": "handler", "type": "logging.FileHandler"}}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "joinedload": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.orm.strategy_options.joinedload", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "make_response": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.make_response", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "send_file": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.send_file", "kind": "Gdef"}, "shape_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond.shape_model", "name": "shape_model", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "token_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.token_required", "kind": "Gdef"}, "validate_diamond_fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["data", "is_update"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.diamond.validate_diamond_fields", "name": "validate_diamond_fields", "type": null}}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\diamond.py"}