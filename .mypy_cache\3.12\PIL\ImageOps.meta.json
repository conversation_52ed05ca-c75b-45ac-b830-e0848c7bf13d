{"data_mtime": 1753088015, "dep_lines": [24, 27, 27, 27, 19, 21, 22, 23, 25, 27, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "PIL.ExifTags", "PIL.Image", "PIL.ImagePalette", "__future__", "functools", "operator", "re", "typing", "PIL", "builtins", "_frozen_importlib", "abc", "enum", "types"], "hash": "2d8a1a23e429861cf00c273dfa4864e88b82bc76", "id": "PIL.ImageOps", "ignore_all": true, "interface_hash": "e6e20a9d7c8c142827cd6e6e5ba873c8b20e7d29", "mtime": 1750655007, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\PIL\\ImageOps.py", "plugin_data": null, "size": 26270, "suppressed": [], "version_id": "1.15.0"}