{".class": "MypyFile", "_fullname": "PIL._imagingcms", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "CapsuleType": {".class": "SymbolTableNode", "cross_ref": "PIL._typing.CapsuleType", "kind": "Gdef", "module_hidden": true, "module_public": false}, "CmsProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL._imagingcms.CmsProfile", "name": "CmsProfile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.CmsProfile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL._imagingcms", "mro": ["PIL._imagingcms.CmsProfile", "builtins.object"], "names": {".class": "SymbolTable", "attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.attributes", "name": "attributes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attributes of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "blue_colorant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.blue_colorant", "name": "blue_colorant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blue_colorant of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.blue_colorant", "name": "blue_colorant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blue_colorant of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "blue_primary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.blue_primary", "name": "blue_primary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blue_primary of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.blue_primary", "name": "blue_primary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blue_primary of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chromatic_adaptation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.chromatic_adaptation", "name": "chromatic_adaptation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chromatic_adaptation of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3x3f"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3x3f"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.chromatic_adaptation", "name": "chromatic_adaptation", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chromatic_adaptation of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3x3f"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3x3f"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chromaticity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.chromaticity", "name": "chromaticity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chromaticity of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.chromaticity", "name": "chromaticity", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chromaticity of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "clut": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.clut", "name": "clut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clut of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.clut", "name": "clut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clut of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "colorant_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.colorant_table", "name": "colorant_table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorant_table of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.colorant_table", "name": "colorant_table", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorant_table of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "colorant_table_out": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.colorant_table_out", "name": "colorant_table_out", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorant_table_out of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.colorant_table_out", "name": "colorant_table_out", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorant_table_out of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "colorimetric_intent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.colorimetric_intent", "name": "colorimetric_intent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorimetric_intent of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.colorimetric_intent", "name": "colorimetric_intent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "colorimetric_intent of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "connection_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.connection_space", "name": "connection_space", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_space of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.connection_space", "name": "connection_space", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connection_space of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copyright": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.copyright", "name": "copyright", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyright of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.copyright", "name": "copyright", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copyright of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "creation_date": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.creation_date", "name": "creation_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "creation_date of CmsProfile", "ret_type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.creation_date", "name": "creation_date", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "creation_date of CmsProfile", "ret_type": {".class": "UnionType", "items": ["datetime.datetime", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "device_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.device_class", "name": "device_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "device_class of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.device_class", "name": "device_class", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "device_class of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "green_colorant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.green_colorant", "name": "green_colorant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "green_colorant of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.green_colorant", "name": "green_colorant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "green_colorant of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "green_primary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.green_primary", "name": "green_primary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "green_primary of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.green_primary", "name": "green_primary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "green_primary of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "header_flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.header_flags", "name": "header_flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header_flags of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.header_flags", "name": "header_flags", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header_flags of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "header_manufacturer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.header_manufacturer", "name": "header_manufacturer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header_manufacturer of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.header_manufacturer", "name": "header_manufacturer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header_manufacturer of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "header_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.header_model", "name": "header_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header_model of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.header_model", "name": "header_model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "header_model of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "icc_measurement_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.icc_measurement_condition", "name": "icc_measurement_condition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icc_measurement_condition of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._IccMeasurementCondition"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.icc_measurement_condition", "name": "icc_measurement_condition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icc_measurement_condition of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._IccMeasurementCondition"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "icc_version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.icc_version", "name": "icc_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icc_version of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.icc_version", "name": "icc_version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icc_version of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "icc_viewing_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.icc_viewing_condition", "name": "icc_viewing_condition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icc_viewing_condition of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._IccViewingCondition"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.icc_viewing_condition", "name": "icc_viewing_condition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "icc_viewing_condition of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._IccViewingCondition"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "intent_supported": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.intent_supported", "name": "intent_supported", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intent_supported of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.intent_supported", "name": "intent_supported", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "intent_supported of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int", {".class": "TupleType", "implicit": false, "items": ["builtins.bool", "builtins.bool", "builtins.bool"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_intent_supported": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.CmsProfile.is_intent_supported", "name": "is_intent_supported", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["PIL._imagingcms.CmsProfile", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_intent_supported of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_matrix_shaper": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.is_matrix_shaper", "name": "is_matrix_shaper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_matrix_shaper of CmsProfile", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.is_matrix_shaper", "name": "is_matrix_shaper", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_matrix_shaper of CmsProfile", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "luminance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.luminance", "name": "luminance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "luminance of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.luminance", "name": "luminance", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "luminance of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "manufacturer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.manufacturer", "name": "manufacturer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "manufacturer of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.manufacturer", "name": "manufacturer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "manufacturer of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "media_black_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.media_black_point", "name": "media_black_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "media_black_point of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.media_black_point", "name": "media_black_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "media_black_point of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "media_white_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.media_white_point", "name": "media_white_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "media_white_point of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.media_white_point", "name": "media_white_point", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "media_white_point of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "media_white_point_temperature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.media_white_point_temperature", "name": "media_white_point_temperature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "media_white_point_temperature of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.media_white_point_temperature", "name": "media_white_point_temperature", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "media_white_point_temperature of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.model", "name": "model", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "model of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "perceptual_rendering_intent_gamut": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.perceptual_rendering_intent_gamut", "name": "perceptual_rendering_intent_gamut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "perceptual_rendering_intent_gamut of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.perceptual_rendering_intent_gamut", "name": "perceptual_rendering_intent_gamut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "perceptual_rendering_intent_gamut of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "profile_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.profile_description", "name": "profile_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_description of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.profile_description", "name": "profile_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_description of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "profile_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.profile_id", "name": "profile_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_id of CmsProfile", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.profile_id", "name": "profile_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_id of CmsProfile", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "red_colorant": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.red_colorant", "name": "red_colorant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "red_colorant of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.red_colorant", "name": "red_colorant", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "red_colorant of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "red_primary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.red_primary", "name": "red_primary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "red_primary of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.red_primary", "name": "red_primary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "red_primary of CmsProfile", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple2x3f"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rendering_intent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.rendering_intent", "name": "rendering_intent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rendering_intent of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.rendering_intent", "name": "rendering_intent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rendering_intent of CmsProfile", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "saturation_rendering_intent_gamut": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.saturation_rendering_intent_gamut", "name": "saturation_rendering_intent_gamut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "saturation_rendering_intent_gamut of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.saturation_rendering_intent_gamut", "name": "saturation_rendering_intent_gamut", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "saturation_rendering_intent_gamut of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "screening_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.screening_description", "name": "screening_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "screening_description of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.screening_description", "name": "screening_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "screening_description of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.target", "name": "target", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "target of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.target", "name": "target", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "target of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "technology": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.technology", "name": "technology", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "technology of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.technology", "name": "technology", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "technology of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "version": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of CmsProfile", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.version", "name": "version", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "version of CmsProfile", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "viewing_condition": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.viewing_condition", "name": "viewing_condition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "viewing_condition of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.viewing_condition", "name": "viewing_condition", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "viewing_condition of CmsProfile", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "xcolor_space": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "PIL._imagingcms.CmsProfile.xcolor_space", "name": "xcolor_space", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xcolor_space of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "PIL._imagingcms.CmsProfile.xcolor_space", "name": "xcolor_space", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "xcolor_space of CmsProfile", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL._imagingcms.CmsProfile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL._imagingcms.CmsProfile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CmsTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL._imagingcms.CmsTransform", "name": "CmsTransform", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.CmsTransform", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL._imagingcms", "mro": ["PIL._imagingcms.CmsTransform", "builtins.object"], "names": {".class": "SymbolTable", "apply": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "id_in", "id_out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.CmsTransform.apply", "name": "apply", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "id_in", "id_out"], "arg_types": ["PIL._imagingcms.CmsTransform", "builtins.object", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "apply of CmsTransform", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "PIL._imagingcms.CmsTransform.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "PIL._imagingcms.CmsTransform", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsFloat": {".class": "SymbolTableNode", "cross_ref": "typing.SupportsFloat", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_IccMeasurementCondition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL._imagingcms._IccMeasurementCondition", "name": "_IccMeasurementCondition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL._imagingcms._IccMeasurementCondition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL._imagingcms", "mro": ["PIL._imagingcms._IccMeasurementCondition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["observer", "builtins.int"], ["backing", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}], ["geo", "builtins.str"], ["flare", "builtins.float"], ["illuminant_type", "builtins.str"]], "readonly_keys": [], "required_keys": ["backing", "flare", "geo", "illuminant_type", "observer"]}}}, "_IccViewingCondition": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "PIL._imagingcms._IccViewingCondition", "name": "_IccViewingCondition", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "PIL._imagingcms._IccViewingCondition", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "PIL._imagingcms", "mro": ["PIL._imagingcms._IccViewingCondition", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["illuminant", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}], ["surround", {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}], ["illuminant_type", "builtins.str"]], "readonly_keys": [], "required_keys": ["illuminant", "illuminant_type", "surround"]}}}, "_Tuple2x3f": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PIL._imagingcms._Tuple2x3f", "line": 10, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_Tuple3f": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PIL._imagingcms._Tuple3f", "line": 9, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.float", "builtins.float", "builtins.float"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_Tuple3x3f": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "PIL._imagingcms._Tuple3x3f", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}, {".class": "TypeAliasType", "args": [], "type_ref": "PIL._imagingcms._Tuple3f"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._imagingcms.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._imagingcms.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._imagingcms.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._imagingcms.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._imagingcms.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._imagingcms.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "buildProofTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.buildProofTransform", "name": "buildProofTransform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1, 1, 1], "arg_names": [null, null, null, null, null, null, null, null], "arg_types": ["PIL._imagingcms.CmsProfile", "PIL._imagingcms.CmsProfile", "PIL._imagingcms.CmsProfile", "builtins.str", "builtins.str", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buildProofTransform", "ret_type": "PIL._imagingcms.CmsTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buildTransform": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": [null, null, null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.buildTransform", "name": "buildTransform", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1], "arg_names": [null, null, null, null, null, null], "arg_types": ["PIL._imagingcms.CmsProfile", "PIL._imagingcms.CmsProfile", "builtins.str", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buildTransform", "ret_type": "PIL._imagingcms.CmsTransform", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "createProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.createProfile", "name": "createProfile", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": [null, null], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "LAB"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "XYZ"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sRGB"}], "uses_pep604_syntax": false}, "typing.SupportsFloat"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "createProfile", "ret_type": "PIL._imagingcms.CmsProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime", "kind": "Gdef", "module_hidden": true, "module_public": false}, "get_display_profile_win32": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.get_display_profile_win32", "name": "get_display_profile_win32", "type": {".class": "CallableType", "arg_kinds": [1, 1], "arg_names": [null, null], "arg_types": ["builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_display_profile_win32", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "littlecms_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "PIL._imagingcms.littlecms_version", "name": "littlecms_version", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "profile_frombytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.profile_frombytes", "name": "profile_frombytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_frombytes", "ret_type": "PIL._imagingcms.CmsProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "profile_open": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.profile_open", "name": "profile_open", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_open", "ret_type": "PIL._imagingcms.CmsProfile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "profile_tobytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "PIL._imagingcms.profile_tobytes", "name": "profile_tobytes", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["PIL._imagingcms.CmsProfile"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "profile_tobytes", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\PIL\\_imagingcms.pyi"}