{"data_mtime": 1753074105, "dep_lines": [4, 5, 9, 10, 3, 11, 2, 6, 7, 8, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["app.models.user", "app.models.token", "app.utils.logger", "app.utils.error_handler", "flask_jwt_extended.exceptions", "app.config", "flask_jwt_extended", "app", "marshmallow", "datetime", "re", "builtins", "_collections_abc", "_frozen_importlib", "abc", "app.models", "app.utils", "flask_jwt_extended.utils", "flask_jwt_extended.view_decorators", "flask_sqlalchemy", "flask_sqlalchemy.extension", "marshmallow.base", "marshmallow.exceptions", "marshmallow.schema", "typing"], "hash": "b559a928b6802ee4ef452b3545ef83c3adb083ce", "id": "app.api.auth", "ignore_all": true, "interface_hash": "d2f8ab4e866799bd31718a2ea3a89982467d3b56", "mtime": 1752242560, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\auth.py", "plugin_data": null, "size": 16101, "suppressed": ["flask_restx"], "version_id": "1.15.0"}