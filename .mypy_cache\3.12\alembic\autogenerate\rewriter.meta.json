{"data_mtime": 1753035443, "dep_lines": [13, 25, 26, 12, 13, 1, 3, 12, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 10, 20, 5, 5, 20, 5, 30, 30, 30, 30, 30], "dependencies": ["alembic.operations.ops", "alembic.runtime.migration", "alembic.script.revision", "alembic.util", "alembic.operations", "__future__", "typing", "alembic", "builtins", "_frozen_importlib", "abc", "alembic.runtime", "alembic.util.langhelpers", "os"], "hash": "33b4f5960098951e520485e89d6a561b4c937e87", "id": "alembic.autogenerate.rewriter", "ignore_all": true, "interface_hash": "472fe04460c082230bf4eee953ce4258d31bb532", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\autogenerate\\rewriter.py", "plugin_data": null, "size": 7814, "suppressed": [], "version_id": "1.15.0"}