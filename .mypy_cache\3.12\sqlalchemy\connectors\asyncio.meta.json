{"data_mtime": 1753035427, "dep_lines": [16, 15, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.util.concurrency", "sqlalchemy.engine", "__future__", "collections", "builtins", "_frozen_importlib", "abc", "asyncio", "asyncio.locks", "asyncio.mixins", "sqlalchemy.engine.interfaces", "sqlalchemy.util._concurrency_py3k", "typing"], "hash": "0720968294bba41a43ec58e0f9716fedeb394b6f", "id": "sqlalchemy.connectors.asyncio", "ignore_all": true, "interface_hash": "1936feb0dddc9876785a2a83360bfb5dc45cd90e", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\connectors\\asyncio.py", "plugin_data": null, "size": 6351, "suppressed": [], "version_id": "1.15.0"}