{".class": "MypyFile", "_fullname": "tomli._parser", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ASCII_CTRL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.ASCII_CTRL", "name": "ASCII_CTRL", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BARE_KEY_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.BARE_KEY_CHARS", "name": "BARE_KEY_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "BASIC_STR_ESCAPE_REPLACEMENTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.BASIC_STR_ESCAPE_REPLACEMENTS", "name": "BASIC_STR_ESCAPE_REPLACEMENTS", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "types.MappingProxyType"}}}, "DEPRECATED_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tomli._parser.DEPRECATED_DEFAULT", "name": "DEPRECATED_DEFAULT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tomli._parser.DEPRECATED_DEFAULT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tomli._parser", "mro": ["tomli._parser.DEPRECATED_DEFAULT", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.DEPRECATED_DEFAULT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tomli._parser.DEPRECATED_DEFAULT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef"}, "Flags": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tomli._parser.Flags", "name": "Flags", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tomli._parser.Flags", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tomli._parser", "mro": ["tomli._parser.Flags", "builtins.object"], "names": {".class": "SymbolTable", "EXPLICIT_NEST": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "final_value": 1, "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.Flags.EXPLICIT_NEST", "name": "EXPLICIT_NEST", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 1}, "type_ref": "builtins.int"}}}, "FROZEN": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "final_value": 0, "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.Flags.FROZEN", "name": "FROZEN", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 0}, "type_ref": "builtins.int"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Flags.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tomli._parser.Flags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Flags", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tomli._parser.Flags._flags", "name": "_flags", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_pending_flags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tomli._parser.Flags._pending_flags", "name": "_pending_flags", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}, "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.set"}}}, "add_pending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "flag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Flags.add_pending", "name": "add_pending", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "flag"], "arg_types": ["tomli._parser.Flags", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_pending of Flags", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "finalize_pending": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Flags.finalize_pending", "name": "finalize_pending", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tomli._parser.Flags"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "finalize_pending of Flags", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "flag"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Flags.is_", "name": "is_", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "flag"], "arg_types": ["tomli._parser.Flags", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_ of Flags", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "key", "flag", "recursive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Flags.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3], "arg_names": ["self", "key", "flag", "recursive"], "arg_types": ["tomli._parser.Flags", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}, "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of Flags", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unset_all": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Flags.unset_all", "name": "unset_all", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["tomli._parser.Flags", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unset_all of Flags", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Flags.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tomli._parser.Flags", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HEXDIGIT_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.HEXDIGIT_CHARS", "name": "HEXDIGIT_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ILLEGAL_BASIC_STR_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.ILLEGAL_BASIC_STR_CHARS", "name": "ILLEGAL_BASIC_STR_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ILLEGAL_COMMENT_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.ILLEGAL_COMMENT_CHARS", "name": "ILLEGAL_COMMENT_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ILLEGAL_LITERAL_STR_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.ILLEGAL_LITERAL_STR_CHARS", "name": "ILLEGAL_LITERAL_STR_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ILLEGAL_MULTILINE_BASIC_STR_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.ILLEGAL_MULTILINE_BASIC_STR_CHARS", "name": "ILLEGAL_MULTILINE_BASIC_STR_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "ILLEGAL_MULTILINE_LITERAL_STR_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.ILLEGAL_MULTILINE_LITERAL_STR_CHARS", "name": "ILLEGAL_MULTILINE_LITERAL_STR_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "IO": {".class": "SymbolTableNode", "cross_ref": "typing.IO", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "KEY_INITIAL_CHARS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.KEY_INITIAL_CHARS", "name": "KEY_INITIAL_CHARS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "Key": {".class": "SymbolTableNode", "cross_ref": "tomli._types.Key", "kind": "Gdef"}, "MAX_INLINE_NESTING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.MAX_INLINE_NESTING", "name": "MAX_INLINE_NESTING", "type": "builtins.int"}}, "MappingProxyType": {".class": "SymbolTableNode", "cross_ref": "types.MappingProxyType", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "NestedDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tomli._parser.NestedDict", "name": "NestedDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tomli._parser.NestedDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tomli._parser", "mro": ["tomli._parser.NestedDict", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.NestedDict.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["tomli._parser.NestedDict"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NestedDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append_nest_to_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.NestedDict.append_nest_to_list", "name": "append_nest_to_list", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["tomli._parser.NestedDict", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append_nest_to_list of NestedDict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dict": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "tomli._parser.NestedDict.dict", "name": "dict", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "get_or_create_nest": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["self", "key", "access_lists"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.NestedDict.get_or_create_nest", "name": "get_or_create_nest", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["self", "key", "access_lists"], "arg_types": ["tomli._parser.NestedDict", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_or_create_nest of NestedDict", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.NestedDict.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tomli._parser.NestedDict", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Output": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tomli._parser.Output", "name": "Output", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "tomli._parser.Output", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["data", "flags"]}}, "module_name": "tomli._parser", "mro": ["tomli._parser.Output", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tomli._parser.Output.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tomli._parser.Output.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tomli._parser.Output.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "data"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flags"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "data", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "tomli._parser.Output.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "data", "flags"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "tomli._parser.NestedDict", "tomli._parser.Flags"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of Output", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Output._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of Output", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tomli._parser.Output._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tomli._parser.Output._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tomli._parser.Output._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "tomli._parser.Output._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Output", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "tomli._parser.Output._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of Output", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "data", "flags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.Output._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "data", "flags"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "tomli._parser.NestedDict", "tomli._parser.Flags"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of Output", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output._NT", "id": -1, "name": "_NT", "namespace": "tomli._parser.Output._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "tomli._parser.Output._source", "name": "_source", "type": "builtins.str"}}, "data": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "tomli._parser.Output.data", "name": "data", "type": "tomli._parser.NestedDict"}}, "data-redefinition": {".class": "SymbolTableNode", "cross_ref": "tomli._parser.Output.data", "kind": "<PERSON><PERSON><PERSON>"}, "flags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "tomli._parser.Output.flags", "name": "flags", "type": "tomli._parser.Flags"}}, "flags-redefinition": {".class": "SymbolTableNode", "cross_ref": "tomli._parser.Output.flags", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.Output.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": "tomli._parser.Output"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": ["tomli._parser.NestedDict", "tomli._parser.Flags"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "ParseFloat": {".class": "SymbolTableNode", "cross_ref": "tomli._types.ParseFloat", "kind": "Gdef"}, "Pos": {".class": "SymbolTableNode", "cross_ref": "tomli._types.Pos", "kind": "Gdef"}, "RE_DATETIME": {".class": "SymbolTableNode", "cross_ref": "tomli._re.RE_DATETIME", "kind": "Gdef"}, "RE_LOCALTIME": {".class": "SymbolTableNode", "cross_ref": "tomli._re.RE_LOCALTIME", "kind": "Gdef"}, "RE_NUMBER": {".class": "SymbolTableNode", "cross_ref": "tomli._re.RE_NUMBER", "kind": "Gdef"}, "TOMLDecodeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.ValueError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tomli._parser.TOMLDecodeError", "name": "TOMLDecodeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tomli._parser.TOMLDecodeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tomli._parser", "mro": ["tomli._parser.TOMLDecodeError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 2], "arg_names": ["self", "msg", "doc", "pos", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.TOMLDecodeError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 2], "arg_names": ["self", "msg", "doc", "pos", "args"], "arg_types": ["tomli._parser.TOMLDecodeError", {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "tomli._parser.DEPRECATED_DEFAULT"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "tomli._parser.DEPRECATED_DEFAULT"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "TypeType", "item": "tomli._parser.DEPRECATED_DEFAULT"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of TOMLDecodeError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "colno": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tomli._parser.TOMLDecodeError.colno", "name": "colno", "type": "builtins.int"}}, "doc": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tomli._parser.TOMLDecodeError.doc", "name": "doc", "type": "builtins.str"}}, "lineno": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tomli._parser.TOMLDecodeError.lineno", "name": "lineno", "type": "builtins.int"}}, "msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tomli._parser.TOMLDecodeError.msg", "name": "msg", "type": "builtins.str"}}, "pos": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "tomli._parser.TOMLDecodeError.pos", "name": "pos", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tomli._parser.TOMLDecodeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tomli._parser.TOMLDecodeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TOML_WS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.TOML_WS", "name": "TOML_WS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "TOML_WS_AND_NEWLINE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_inferred", "has_explicit_value"], "fullname": "tomli._parser.TOML_WS_AND_NEWLINE", "name": "TOML_WS_AND_NEWLINE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tomli._parser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tomli._parser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tomli._parser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tomli._parser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tomli._parser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tomli._parser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "create_dict_rule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.create_dict_rule", "name": "create_dict_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "out"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._parser.Output"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_dict_rule", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_list_rule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "out"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.create_list_rule", "name": "create_list_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "out"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._parser.Output"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_list_rule", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_unicode_scalar_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["codepoint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.is_unicode_scalar_value", "name": "is_unicode_scalar_value", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["codepoint"], "arg_types": ["builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_unicode_scalar_value", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_value_rule": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["src", "pos", "out", "header", "parse_float"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.key_value_rule", "name": "key_value_rule", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["src", "pos", "out", "header", "parse_float"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._parser.Output"}, {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}, {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "key_value_rule", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": [null, "parse_float"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": [null, "parse_float"], "arg_types": [{".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loads": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5], "arg_names": [null, "parse_float"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.loads", "name": "loads", "type": {".class": "CallableType", "arg_kinds": [0, 5], "arg_names": [null, "parse_float"], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loads", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_safe_parse_float": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parse_float"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.make_safe_parse_float", "name": "make_safe_parse_float", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parse_float"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_safe_parse_float", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "match_to_datetime": {".class": "SymbolTableNode", "cross_ref": "tomli._re.match_to_datetime", "kind": "Gdef"}, "match_to_localtime": {".class": "SymbolTableNode", "cross_ref": "tomli._re.match_to_localtime", "kind": "Gdef"}, "match_to_number": {".class": "SymbolTableNode", "cross_ref": "tomli._re.match_to_number", "kind": "Gdef"}, "parse_array": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_array", "name": "parse_array", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_array", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_basic_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["src", "pos", "multiline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_basic_str", "name": "parse_basic_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["src", "pos", "multiline"], "arg_types": ["builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_basic_str", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_basic_str_escape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5], "arg_names": ["src", "pos", "multiline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_basic_str_escape", "name": "parse_basic_str_escape", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5], "arg_names": ["src", "pos", "multiline"], "arg_types": ["builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_basic_str_escape", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_basic_str_escape_multiline": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_basic_str_escape_multiline", "name": "parse_basic_str_escape_multiline", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_basic_str_escape_multiline", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_hex_char": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "hex_len"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_hex_char", "name": "parse_hex_char", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "hex_len"], "arg_types": ["builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_hex_char", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_inline_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_inline_table", "name": "parse_inline_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_inline_table", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_key", "name": "parse_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_key", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_key_part": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_key_part", "name": "parse_key_part", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_key_part", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_key_value_pair": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_key_value_pair", "name": "parse_key_value_pair", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_key_value_pair", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.Key"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_literal_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_literal_str", "name": "parse_literal_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_literal_str", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_multiline_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["src", "pos", "literal"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_multiline_str", "name": "parse_multiline_str", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["src", "pos", "literal"], "arg_types": ["builtins.str", "builtins.int", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_multiline_str", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_one_line_basic_str": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_one_line_basic_str", "name": "parse_one_line_basic_str", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_one_line_basic_str", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_value": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.parse_value", "name": "parse_value", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["src", "pos", "parse_float", "nest_lvl"], "arg_types": ["builtins.str", "builtins.int", {".class": "TypeAliasType", "args": [], "type_ref": "tomli._types.ParseFloat"}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_value", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip_chars": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "chars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.skip_chars", "name": "skip_chars", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["src", "pos", "chars"], "arg_types": ["builtins.str", "builtins.int", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_chars", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.skip_comment", "name": "skip_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_comment", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip_comments_and_array_ws": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.skip_comments_and_array_ws", "name": "skip_comments_and_array_ws", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["src", "pos"], "arg_types": ["builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_comments_and_array_ws", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "skip_until": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 3, 3], "arg_names": ["src", "pos", "expect", "error_on", "error_on_eof"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tomli._parser.skip_until", "name": "skip_until", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 3, 3], "arg_names": ["src", "pos", "expect", "error_on", "error_on_eof"], "arg_types": ["builtins.str", "builtins.int", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "skip_until", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "string": {".class": "SymbolTableNode", "cross_ref": "string", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\tomli\\_parser.py"}