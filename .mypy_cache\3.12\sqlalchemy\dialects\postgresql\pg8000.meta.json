{"data_mtime": 1753035438, "dep_lines": [101, 102, 103, 112, 115, 117, 101, 120, 121, 122, 118, 119, 120, 121, 98, 99, 118, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 10, 10, 5, 10, 10, 20, 20, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.pg_catalog", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.engine.processors", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.elements", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "decimal", "re", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "typing"], "hash": "f8d14c70e9a62007b964f7cfffa6ef8e57c9568c", "id": "sqlalchemy.dialects.postgresql.pg8000", "ignore_all": true, "interface_hash": "f098b35179d8ab522a0a6a28525fc6192c09dcc2", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\pg8000.py", "plugin_data": null, "size": 19304, "suppressed": [], "version_id": "1.15.0"}