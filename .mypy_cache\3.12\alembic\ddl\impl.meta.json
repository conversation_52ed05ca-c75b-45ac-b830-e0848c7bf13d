{"data_mtime": 1753035443, "dep_lines": [32, 33, 37, 45, 46, 49, 50, 54, 55, 58, 59, 27, 32, 36, 43, 47, 4, 6, 7, 8, 23, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 25, 25, 25, 25, 25, 25, 25, 25, 10, 20, 10, 25, 25, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.ddl._autogen", "alembic.ddl.base", "alembic.util.sqla_compat", "sqlalchemy.engine.cursor", "sqlalchemy.engine.reflection", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.operations.batch", "sqlalchemy.schema", "alembic.ddl", "alembic.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "logging", "re", "typing", "sqlalchemy", "alembic", "builtins", "_frozen_importlib", "abc", "alembic.autogenerate", "alembic.operations", "alembic.util.exc", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers"], "hash": "111ebe3fdd7bd823abc0365f627755c82170ff96", "id": "alembic.ddl.impl", "ignore_all": true, "interface_hash": "c285e38827caf0db24e67107d1cd4e9148573962", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\ddl\\impl.py", "plugin_data": null, "size": 30439, "suppressed": [], "version_id": "1.15.0"}