{"data_mtime": 1753035443, "dep_lines": [26, 27, 28, 34, 36, 37, 42, 48, 49, 21, 23, 25, 26, 31, 33, 36, 4, 6, 7, 8, 9, 21, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 10, 25, 25, 25, 10, 10, 10, 20, 5, 10, 20, 5, 10, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.expression", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "alembic.ddl._autogen", "alembic.operations.ops", "alembic.util.sqla_compat", "sqlalchemy.engine.reflection", "alembic.autogenerate.api", "alembic.ddl.impl", "sqlalchemy.event", "sqlalchemy.schema", "sqlalchemy.types", "sqlalchemy.sql", "sqlalchemy.util", "alembic.util", "alembic.operations", "__future__", "contextlib", "logging", "re", "typing", "sqlalchemy", "alembic", "builtins", "_frozen_importlib", "abc", "alembic.ddl", "alembic.util.langhelpers", "sqlalchemy.engine", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "5910b350350e9392b87a57553ad2029541050cdd", "id": "alembic.autogenerate.compare", "ignore_all": true, "interface_hash": "815a0ecac81bb5d33faaa128a7524ca19c8f4189", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\autogenerate\\compare.py", "plugin_data": null, "size": 45979, "suppressed": [], "version_id": "1.15.0"}