{".class": "MypyFile", "_fullname": "app.api.vendor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc.IntegrityError", "kind": "Gdef"}, "JewelryItem": {".class": "SymbolTableNode", "cross_ref": "app.models.jewelry.JewelryItem", "kind": "Gdef"}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.vendor.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.vendor.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Resource", "source_any": null, "type_of_any": 3}}}, "Vendor": {".class": "SymbolTableNode", "cross_ref": "app.models.vendor.Vendor", "kind": "Gdef"}, "VendorList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.vendor.VendorList", "name": "VendorList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.vendor.VendorList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.vendor", "mro": ["app.api.vendor.VendorList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.vendor.VendorList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.vendor.VendorList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.vendor.VendorList.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.vendor.VendorList.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.vendor.VendorList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.vendor.VendorList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VendorResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.vendor.VendorResource", "name": "VendorResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.vendor.VendorResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.vendor", "mro": ["app.api.vendor.VendorResource", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vendor_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.vendor.VendorResource.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.vendor.VendorResource.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vendor_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.vendor.VendorResource.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.vendor.VendorResource.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "vendor_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.vendor.VendorResource.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.vendor.VendorResource.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.vendor.VendorResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.vendor.VendorResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.vendor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.vendor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.vendor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.vendor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.vendor.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.vendor.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.vendor.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.vendor.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.fields", "source_any": null, "type_of_any": 3}}}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "token_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.token_required", "kind": "Gdef"}, "vendor_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.vendor.vendor_model", "name": "vendor_model", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "vendor_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.vendor.vendor_ns", "name": "vendor_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "vendor_to_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["v"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.vendor.vendor_to_dict", "name": "vendor_to_dict", "type": null}}, "vendor_update_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.vendor.vendor_update_model", "name": "vendor_update_model", "type": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.vendor.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\vendor.py"}