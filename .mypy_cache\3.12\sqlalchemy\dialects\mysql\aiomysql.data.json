{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql.aiomysql", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AdaptedConnection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.AdaptedConnection", "kind": "Gdef"}, "AsyncAdaptFallback_aiomysql_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdaptFallback_aiomysql_connection", "name": "AsyncAdaptFallback_aiomysql_connection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdaptFallback_aiomysql_connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.aiomysql", "mro": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdaptFallback_aiomysql_connection", "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection", "sqlalchemy.engine.interfaces.AdaptedConnection", "builtins.object"], "names": {".class": "SymbolTable", "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdaptFallback_aiomysql_connection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "await_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdaptFallback_aiomysql_connection.await_", "name": "await_", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["awaitable"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._R_co", "id": 1710, "name": "_R_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "imprecise_arg_kinds": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._R_co", "id": 1710, "name": "_R_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._R_co", "id": 1710, "name": "_R_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdaptFallback_aiomysql_connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdaptFallback_aiomysql_connection", "values": [], "variance": 0}, "slots": ["_connection", "_execute_mutex", "dbapi"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_aiomysql_connection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.engine.interfaces.AdaptedConnection"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection", "name": "AsyncAdapt_aiomysql_connection", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.aiomysql", "mro": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection", "sqlalchemy.engine.interfaces.AdaptedConnection", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_execute_mutex": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection._execute_mutex", "name": "_execute_mutex", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "autocommit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.autocommit", "name": "autocommit", "type": null}}, "await_": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.await_", "name": "await_", "type": {".class": "Instance", "args": [{".class": "Parameters", "arg_kinds": [0], "arg_names": ["awaitable"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._R_co", "id": 1710, "name": "_R_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "typing.Awaitable"}], "imprecise_arg_kinds": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._R_co", "id": 1710, "name": "_R_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}]}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "builtins._R_co", "id": 1710, "name": "_R_co", "namespace": "", "upper_bound": "builtins.object", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.staticmethod"}}}, "character_set_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.character_set_name", "name": "character_set_name", "type": null}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "close of AsyncAdapt_aiomysql_connection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.commit", "name": "commit", "type": null}}, "cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "server_side"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.cursor", "name": "cursor", "type": null}}, "dbapi": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.dbapi", "name": "dbapi", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "reconnect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.ping", "name": "ping", "type": null}}, "rollback": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.rollback", "name": "rollback", "type": null}}, "terminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.terminate", "name": "terminate", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_connection", "values": [], "variance": 0}, "slots": ["_connection", "_execute_mutex", "dbapi"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_aiomysql_cursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor", "name": "AsyncAdapt_aiomysql_cursor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.aiomysql", "mro": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "adapt_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.__iter__", "name": "__iter__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_adapt_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor._adapt_connection", "name": "_adapt_connection", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_connection": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor._connection", "name": "_connection", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor._cursor", "name": "_cursor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_execute_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor._execute_async", "name": "_execute_async", "type": null}}, "_executemany_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation", "seq_of_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor._executemany_async", "name": "_executemany_async", "type": null}}, "_rows": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor._rows", "name": "_rows", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "arraysize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.arraysize", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.arraysize", "name": "arraysize", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.arraysize", "name": "arraysize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arraysize of AsyncAdapt_aiomysql_cursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.arraysize", "name": "arraysize", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "arraysize", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "arraysize", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "await_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.await_", "name": "await_", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.close", "name": "close", "type": null}}, "description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.description", "name": "description", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.description", "name": "description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "description of AsyncAdapt_aiomysql_cursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "execute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "operation", "parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.execute", "name": "execute", "type": null}}, "executemany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "operation", "seq_of_parameters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.executemany", "name": "executemany", "type": null}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.fetchall", "name": "fetchall", "type": null}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.fetchmany", "name": "fetchmany", "type": null}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.fetchone", "name": "fetchone", "type": null}}, "lastrowid": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.lastrowid", "name": "lastrowid", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.lastrowid", "name": "lastrowid", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "lastrowid of AsyncAdapt_aiomysql_cursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.rowcount", "name": "rowcount", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.rowcount", "name": "rowcount", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rowcount of AsyncAdapt_aiomysql_cursor", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "server_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.server_side", "name": "server_side", "type": "builtins.bool"}}, "setinputsizes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["self", "inputsizes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.setinputsizes", "name": "setinputsizes", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor", "values": [], "variance": 0}, "slots": ["_adapt_connection", "_connection", "_cursor", "_rows", "await_"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_aiomysql_dbapi": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi", "name": "AsyncAdapt_aiomysql_dbapi", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.aiomysql", "mro": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi", "builtins.object"], "names": {".class": "SymbolTable", "Cursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.Cursor", "name": "<PERSON><PERSON><PERSON>", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "SSCursor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.SSCursor", "name": "SSCursor", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "aiomysql", "pymysql"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.__init__", "name": "__init__", "type": null}}, "_init_cursors_subclasses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi._init_cursors_subclasses", "name": "_init_cursors_subclasses", "type": null}}, "_init_dbapi_attributes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi._init_dbapi_attributes", "name": "_init_dbapi_attributes", "type": null}}, "aiomysql": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.aiomysql", "name": "aiomysql", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.connect", "name": "connect", "type": null}}, "paramstyle": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.paramstyle", "name": "paramstyle", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "pymysql": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.pymysql", "name": "pymysql", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_dbapi", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAdapt_aiomysql_ss_cursor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor", "name": "AsyncAdapt_aiomysql_ss_cursor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.aiomysql", "mro": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor", "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_cursor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "adapt_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": [], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.close", "name": "close", "type": null}}, "fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.fetchall", "name": "fetchall", "type": null}}, "fetchmany": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "size"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.fetchmany", "name": "fetchmany", "type": null}}, "fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.fetchone", "name": "fetchone", "type": null}}, "server_side": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.server_side", "name": "server_side", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor", "values": [], "variance": 0}, "slots": ["_adapt_connection", "_connection", "_cursor", "_rows", "await_"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLDialect_aiomysql": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.pymysql.MySQLDialect_pymysql"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql", "name": "MySQLDialect_aiomysql", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.aiomysql", "mro": ["sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql", "sqlalchemy.dialects.mysql.pymysql.MySQLDialect_pymysql", "sqlalchemy.dialects.mysql.mysqldb.MySQLDialect_mysqldb", "sqlalchemy.dialects.mysql.base.MySQLDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "_found_rows_client_flag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql._found_rows_client_flag", "name": "_found_rows_client_flag", "type": null}}, "_sscursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql._sscursor", "name": "_sscursor", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["adapt_connection"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": ["sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.aiomysql.AsyncAdapt_aiomysql_ss_cursor", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.create_connect_args", "name": "create_connect_args", "type": null}}, "do_terminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.do_terminate", "name": "do_terminate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "arg_types": ["sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "do_terminate of MySQLDialect_aiomysql", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.driver", "name": "driver", "type": "builtins.str"}}, "get_driver_connection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.get_driver_connection", "name": "get_driver_connection", "type": null}}, "get_pool_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.get_pool_class", "name": "get_pool_class", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.get_pool_class", "name": "get_pool_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pool_class of MySQLDialect_aiomysql", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "has_terminate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.has_terminate", "name": "has_terminate", "type": "builtins.bool"}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.import_dbapi", "name": "import_dbapi", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of MySQLDialect_aiomysql", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.is_async", "name": "is_async", "type": "builtins.bool"}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.is_disconnect", "name": "is_disconnect", "type": null}}, "supports_server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.supports_server_side_cursors", "name": "supports_server_side_cursors", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLDialect_pymysql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.pymysql.MySQLDialect_pymysql", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.aiomysql.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "await_fallback": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_fallback", "kind": "Gdef"}, "await_only": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._concurrency_py3k.await_only", "kind": "Gdef"}, "deque": {".class": "SymbolTableNode", "cross_ref": "collections.deque", "kind": "Gdef"}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.aiomysql.dialect", "line": 335, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.aiomysql.MySQLDialect_aiomysql"}}, "pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\aiomysql.py"}