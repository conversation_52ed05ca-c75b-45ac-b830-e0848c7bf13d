{"data_mtime": 1753035438, "dep_lines": [187, 188, 189, 190, 202, 187, 209, 210, 211, 205, 206, 207, 208, 210, 179, 181, 182, 183, 184, 185, 205, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1014], "dep_prios": [10, 10, 5, 5, 5, 20, 10, 10, 5, 10, 10, 10, 5, 20, 5, 5, 10, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.types", "sqlalchemy.dialects.postgresql", "sqlalchemy.engine.processors", "sqlalchemy.sql.sqltypes", "sqlalchemy.util.concurrency", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "collections", "decimal", "json", "re", "time", "sqlalchemy", "builtins", "_asyncio", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "asyncio", "asyncio.exceptions", "asyncio.locks", "asyncio.mixins", "enum", "sqlalchemy.dialects.postgresql.named_types", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._collections", "sqlalchemy.util._concurrency_py3k", "sqlalchemy.util.langhelpers", "types", "typing"], "hash": "09fffaf487e179da89639a3f72dad854bb54fdeb", "id": "sqlalchemy.dialects.postgresql.asyncpg", "ignore_all": true, "interface_hash": "67cc72b42dbc0ade5433ad9454de76ca17b5c7b8", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\asyncpg.py", "plugin_data": null, "size": 42574, "suppressed": ["asyncpg"], "version_id": "1.15.0"}