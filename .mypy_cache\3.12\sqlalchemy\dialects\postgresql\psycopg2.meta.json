{"data_mtime": 1753035438, "dep_lines": [498, 499, 501, 502, 498, 493, 504, 505, 491, 493, 494, 495, 496, 504, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 696], "dep_prios": [10, 5, 5, 5, 20, 10, 10, 5, 5, 20, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.postgresql.ranges", "sqlalchemy.dialects.postgresql._psycopg_common", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql", "collections.abc", "sqlalchemy.types", "sqlalchemy.util", "__future__", "collections", "logging", "re", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql", "sqlalchemy.sql.compiler", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util.langhelpers", "types"], "hash": "0d2ff6dbeabfdcb5a50733190cca2be61c6487ff", "id": "sqlalchemy.dialects.postgresql.psycopg2", "ignore_all": true, "interface_hash": "0c9102f43ceb9a17f9203627deb02b2569cc77c4", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\psycopg2.py", "plugin_data": null, "size": 32924, "suppressed": ["psycopg2"], "version_id": "1.15.0"}