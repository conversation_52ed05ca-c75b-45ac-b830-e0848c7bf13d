{"data_mtime": 1753035443, "dep_lines": [19, 24, 26, 29, 30, 31, 33, 36, 38, 16, 17, 25, 4, 6, 7, 13, 25, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 25, 25, 25, 25, 10, 10, 10, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.ddl.base", "alembic.ddl.impl", "alembic.util.sqla_compat", "sqlalchemy.engine.reflection", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.operations.batch", "sqlalchemy.schema", "sqlalchemy.sql", "alembic.util", "__future__", "re", "typing", "sqlalchemy", "alembic", "builtins", "_frozen_importlib", "abc", "alembic.operations", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "924b36a6b74d14a2fed196d4d6da6602996c1900", "id": "alembic.ddl.sqlite", "ignore_all": true, "interface_hash": "a111bf5b7fc499120e214fcede066c9f4cdd8a42", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\ddl\\sqlite.py", "plugin_data": null, "size": 8006, "suppressed": [], "version_id": "1.15.0"}