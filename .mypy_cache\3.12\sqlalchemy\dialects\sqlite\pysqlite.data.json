{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.sqlite.pysqlite", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.sqlite.base.DATE", "kind": "Gdef"}, "DATETIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.sqlite.base.DATETIME", "kind": "Gdef"}, "SQLiteDialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.sqlite.base.SQLiteDialect", "kind": "Gdef"}, "SQLiteDialect_pysqlite": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.sqlite.base.SQLiteDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite", "name": "SQLiteDialect_pysqlite", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.sqlite.pysqlite", "mro": ["sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite", "sqlalchemy.dialects.sqlite.base.SQLiteDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "_get_server_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite._get_server_version_info", "name": "_get_server_version_info", "type": null}}, "_is_url_file_db": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite._is_url_file_db", "name": "_is_url_file_db", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite._is_url_file_db", "name": "_is_url_file_db", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_url_file_db of SQLiteDialect_pysqlite", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_isolation_lookup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite._isolation_lookup", "name": "_isolation_lookup", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "sqlalchemy.util._py_collections.immutabledict"}}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.create_connect_args", "name": "create_connect_args", "type": null}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "description_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.description_encoding", "name": "description_encoding", "type": {".class": "NoneType"}}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.driver", "name": "driver", "type": "builtins.str"}}, "get_pool_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.get_pool_class", "name": "get_pool_class", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.get_pool_class", "name": "get_pool_class", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "url"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_pool_class of SQLiteDialect_pysqlite", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.import_dbapi", "name": "import_dbapi", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of SQLiteDialect_pysqlite", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.is_disconnect", "name": "is_disconnect", "type": null}}, "on_connect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.on_connect", "name": "on_connect", "type": null}}, "returns_native_bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.returns_native_bytes", "name": "returns_native_bytes", "type": "builtins.bool"}}, "set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dbapi_connection", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.set_isolation_level", "name": "set_isolation_level", "type": null}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SQLiteDialect_pysqlite_dollar": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar", "name": "_SQLiteDialect_pysqlite_dollar", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.sqlite.pysqlite", "mro": ["sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar", "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric", "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite", "sqlalchemy.dialects.sqlite.base.SQLiteDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar.__init__", "name": "__init__", "type": null}}, "_first_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar._first_bind", "name": "_first_bind", "type": "builtins.str"}}, "_not_in_statement_regexp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar._not_in_statement_regexp", "name": "_not_in_statement_regexp", "type": null}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar.driver", "name": "driver", "type": "builtins.str"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_dollar", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SQLiteDialect_pysqlite_numeric": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric", "name": "_SQLiteDialect_pysqlite_numeric", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.sqlite.pysqlite", "mro": ["sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric", "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite", "sqlalchemy.dialects.sqlite.base.SQLiteDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric.__init__", "name": "__init__", "type": null}}, "_first_bind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric._first_bind", "name": "_first_bind", "type": "builtins.str"}}, "_fix_sqlite_issue_99953": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric._fix_sqlite_issue_99953", "name": "_fix_sqlite_issue_99953", "type": null}}, "_not_in_statement_regexp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric._not_in_statement_regexp", "name": "_not_in_statement_regexp", "type": {".class": "NoneType"}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric.create_connect_args", "name": "create_connect_args", "type": null}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric.driver", "name": "driver", "type": "builtins.str"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.sqlite.pysqlite._SQLiteDialect_pysqlite_numeric", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SQLite_pysqliteDate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.sqlite.base.DATE"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteDate", "name": "_SQLite_pysqliteDate", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteDate", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.sqlite.pysqlite", "mro": ["sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteDate", "sqlalchemy.dialects.sqlite.base.DATE", "sqlalchemy.dialects.sqlite.base._DateTimeMixin", "sqlalchemy.sql.sqltypes.Date", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteDate.bind_processor", "name": "bind_processor", "type": null}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteDate.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteDate.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteDate", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_SQLite_pysqliteTimeStamp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.sqlite.base.DATETIME"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteTimeStamp", "name": "_SQLite_pysqliteTimeStamp", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteTimeStamp", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.sqlite.pysqlite", "mro": ["sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteTimeStamp", "sqlalchemy.dialects.sqlite.base.DATETIME", "sqlalchemy.dialects.sqlite.base._DateTimeMixin", "sqlalchemy.sql.sqltypes.DateTime", "sqlalchemy.sql.sqltypes._RenderISO8601NoT", "sqlalchemy.sql.sqltypes.HasExpressionLookup", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteTimeStamp.bind_processor", "name": "bind_processor", "type": null}}, "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteTimeStamp.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteTimeStamp.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.sqlite.pysqlite._SQLite_pysqliteTimeStamp", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.sqlite.pysqlite.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.sqlite.pysqlite.dialect", "line": 598, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.sqlite.pysqlite.SQLiteDialect_pysqlite"}}, "exc": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc", "kind": "Gdef"}, "math": {".class": "SymbolTableNode", "cross_ref": "math", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pool": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.pool", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlite.py"}