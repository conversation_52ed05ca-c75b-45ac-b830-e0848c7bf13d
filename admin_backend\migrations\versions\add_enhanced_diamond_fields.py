"""Add enhanced diamond fields for professional grading

Revision ID: enhanced_diamond_fields
Revises: 
Create Date: 2025-01-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import sqlite

# revision identifiers, used by Alembic.
revision = 'enhanced_diamond_fields'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    """Add enhanced diamond fields for professional grading system."""
    
    # Add new columns to diamonds table
    with op.batch_alter_table('diamonds', schema=None) as batch_op:
        # Additional Grading Properties
        batch_op.add_column(sa.Column('cut_grade', sa.String(20), nullable=True))
        batch_op.add_column(sa.Column('polish', sa.String(20), nullable=True))
        batch_op.add_column(sa.Column('symmetry', sa.String(20), nullable=True))
        batch_op.add_column(sa.Column('fluorescence', sa.String(30), nullable=True))
        batch_op.add_column(sa.Column('fluorescence_color', sa.String(20), nullable=True))
        
        # Measurements
        batch_op.add_column(sa.Column('length_mm', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('width_mm', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('depth_mm', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('depth_percent', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('table_percent', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('girdle', sa.String(50), nullable=True))
        batch_op.add_column(sa.Column('culet', sa.String(20), nullable=True))
        
        # Enhanced Certification
        batch_op.add_column(sa.Column('certification_lab', sa.String(50), nullable=True))
        batch_op.add_column(sa.Column('certificate_date', sa.Date(), nullable=True))
        batch_op.add_column(sa.Column('certificate_url', sa.String(500), nullable=True))
        
        # Pricing
        batch_op.add_column(sa.Column('cost_price', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('retail_price', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('market_value', sa.Float(), nullable=True))
        batch_op.add_column(sa.Column('last_valuation_date', sa.Date(), nullable=True))
        
        # Enhanced Inventory Management
        batch_op.add_column(sa.Column('reserved_quantity', sa.Integer(), nullable=True, default=0))
        batch_op.add_column(sa.Column('available_quantity', sa.Integer(), nullable=True, default=1))
        batch_op.add_column(sa.Column('minimum_stock', sa.Integer(), nullable=True, default=1))
        
        # Additional Fields
        batch_op.add_column(sa.Column('location', sa.String(100), nullable=True))
        batch_op.add_column(sa.Column('notes', sa.Text(), nullable=True))
        
        # Timestamps
        batch_op.add_column(sa.Column('created_at', sa.DateTime(), nullable=True, default=sa.func.current_timestamp()))
        batch_op.add_column(sa.Column('updated_at', sa.DateTime(), nullable=True, default=sa.func.current_timestamp()))
        
        # Update color and clarity columns to use proper constraints
        batch_op.alter_column('color', type_=sa.String(5))
        batch_op.alter_column('clarity', type_=sa.String(10))
        
        # Extend certificate_no length
        batch_op.alter_column('certificate_no', type_=sa.String(100))


def downgrade():
    """Remove enhanced diamond fields."""
    
    with op.batch_alter_table('diamonds', schema=None) as batch_op:
        # Remove added columns
        batch_op.drop_column('cut_grade')
        batch_op.drop_column('polish')
        batch_op.drop_column('symmetry')
        batch_op.drop_column('fluorescence')
        batch_op.drop_column('fluorescence_color')
        batch_op.drop_column('length_mm')
        batch_op.drop_column('width_mm')
        batch_op.drop_column('depth_mm')
        batch_op.drop_column('depth_percent')
        batch_op.drop_column('table_percent')
        batch_op.drop_column('girdle')
        batch_op.drop_column('culet')
        batch_op.drop_column('certification_lab')
        batch_op.drop_column('certificate_date')
        batch_op.drop_column('certificate_url')
        batch_op.drop_column('cost_price')
        batch_op.drop_column('retail_price')
        batch_op.drop_column('market_value')
        batch_op.drop_column('last_valuation_date')
        batch_op.drop_column('reserved_quantity')
        batch_op.drop_column('available_quantity')
        batch_op.drop_column('minimum_stock')
        batch_op.drop_column('location')
        batch_op.drop_column('notes')
        batch_op.drop_column('created_at')
        batch_op.drop_column('updated_at')
        
        # Revert column types
        batch_op.alter_column('color', type_=sa.String(20))
        batch_op.alter_column('clarity', type_=sa.String(20))
        batch_op.alter_column('certificate_no', type_=sa.String(100))
