{"data_mtime": 1753041215, "dep_lines": [6, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "builtins", "_frozen_importlib", "_typeshed", "abc", "http", "http.cookiejar", "requests.auth", "requests.models", "typing"], "hash": "8f4f82ec658029c3696669bfa281056a4bc07b40", "id": "simple_diamond_test", "ignore_all": false, "interface_hash": "d270f21ccbc7aa431f2e3f833c70b425188bb02a", "mtime": 1753041214, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\simple_diamond_test.py", "plugin_data": null, "size": 1229, "suppressed": [], "version_id": "1.15.0"}