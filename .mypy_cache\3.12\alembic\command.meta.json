{"data_mtime": 1753035443, "dep_lines": [14, 16, 20, 21, 12, 13, 15, 19, 3, 5, 6, 7, 12, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 25, 25, 10, 10, 5, 25, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.runtime.environment", "alembic.util.compat", "alembic.script.base", "alembic.script.revision", "alembic.autogenerate", "alembic.util", "alembic.script", "alembic.config", "__future__", "os", "pathlib", "typing", "alembic", "builtins", "_frozen_importlib", "abc", "alembic.operations", "alembic.operations.ops", "alembic.runtime", "alembic.runtime.migration"], "hash": "1e5fbb568ad6f9a2bb246bf29b902cdbbdd9744b", "id": "alembic.command", "ignore_all": true, "interface_hash": "4eebe18b23ebed988c893d725fc20f49c42aacaa", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\command.py", "plugin_data": null, "size": 24855, "suppressed": [], "version_id": "1.15.0"}