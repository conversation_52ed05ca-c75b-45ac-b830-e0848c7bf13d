{"data_mtime": 1753035443, "dep_lines": [22, 23, 25, 26, 28, 22, 24, 25, 34, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 24, 42, 1, 1, 1, 1, 1, 1, 1, 1, 1, 45], "dep_prios": [10, 10, 10, 10, 5, 20, 5, 20, 25, 5, 5, 10, 10, 5, 10, 10, 10, 5, 5, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["alembic.script.revision", "alembic.script.write_hooks", "alembic.runtime.migration", "alembic.util.compat", "alembic.util.pyfiles", "alembic.script", "alembic.util", "alembic.runtime", "alembic.config", "__future__", "contextlib", "datetime", "os", "pathlib", "re", "shutil", "sys", "types", "typing", "alembic", "zoneinfo", "builtins", "_frozen_importlib", "abc", "alembic.util.exc", "alembic.util.langhelpers", "enum", "sqlalchemy", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "cd3111f2ba54fbbfe52737893f841a5e7791b425", "id": "alembic.script.base", "ignore_all": true, "interface_hash": "01619ce303467f7aedeb8bec9c18f18f7166d70e", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\script\\base.py", "plugin_data": null, "size": 36924, "suppressed": ["backports.zoneinfo"], "version_id": "1.15.0"}