{"data_mtime": 1753035433, "dep_lines": [989, 998, 999, 1000, 1002, 1003, 1004, 1005, 1006, 992, 993, 994, 996, 997, 998, 982, 984, 985, 986, 987, 992, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 10, 10, 10, 10, 10, 10, 10, 10, 5, 10, 20, 5, 10, 10, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.sqlite.json", "sqlalchemy.engine.default", "sqlalchemy.engine.processors", "sqlalchemy.engine.reflection", "sqlalchemy.sql.coercions", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.schema", "sqlalchemy.exc", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "datetime", "numbers", "re", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "decimal", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.log", "sqlalchemy.sql._typing", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.ddl", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.operators", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "f070c48645745033a57a1b8b1b112f5c7777bcc0", "id": "sqlalchemy.dialects.sqlite.base", "ignore_all": true, "interface_hash": "d5a592115a421254b5c62541a2ce61aa19b62e65", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\base.py", "plugin_data": null, "size": 105514, "suppressed": [], "version_id": "1.15.0"}