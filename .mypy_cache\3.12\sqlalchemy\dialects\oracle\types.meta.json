{"data_mtime": 1753035428, "dep_lines": [16, 21, 22, 15, 16, 17, 8, 10, 11, 15, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 25, 25, 10, 20, 5, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.type_api", "sqlalchemy.exc", "sqlalchemy.sql", "sqlalchemy.types", "__future__", "datetime", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.base", "sqlalchemy.sql.visitors"], "hash": "857646479cf30b3f4f71a6800958fad772c83f32", "id": "sqlalchemy.dialects.oracle.types", "ignore_all": true, "interface_hash": "9cef5923192b1d546dec0c3504eb4c64b173e75e", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\types.py", "plugin_data": null, "size": 9374, "suppressed": [], "version_id": "1.15.0"}