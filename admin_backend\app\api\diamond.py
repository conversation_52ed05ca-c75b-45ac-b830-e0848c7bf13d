# This file includes strengthened backend validation, audit logging, and error handling for production use.
from flask_restx import Namespace, Resource, fields
from flask import request, make_response, send_file
from app.models.diamond import Diamond, Shape, Manufacturing, Jewelry
from app.models.vendor import Vendor
from app import db
from app.utils.decorators import token_required, admin_required
from datetime import datetime
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import joinedload
from functools import wraps
import re
import logging
import csv
import io
from tempfile import NamedTemporaryFile

diamond_ns = Namespace('diamonds', description='Diamond Inventory', path='/')

diamond_model = diamond_ns.model('Diamond', {
    'id': fields.Integer(readOnly=True),
    'shape': fields.String(required=False),
    'shape_id': fields.Integer(required=True),

    # Basic Properties
    'carat': fields.Float(required=True),

    # 4Cs - Industry Standard
    'color': fields.String(required=True, description='Diamond color grade (D-Z)'),
    'clarity': fields.String(required=True, description='Diamond clarity grade (FL, IF, VVS1, etc.)'),
    'cut_grade': fields.String(description='Cut grade (Excellent, Very Good, etc.)'),

    # Additional Grading
    'polish': fields.String(description='Polish grade'),
    'symmetry': fields.String(description='Symmetry grade'),
    'fluorescence': fields.String(description='Fluorescence intensity'),
    'fluorescence_color': fields.String(description='Fluorescence color'),

    # Measurements
    'length_mm': fields.Float(description='Length in millimeters'),
    'width_mm': fields.Float(description='Width in millimeters'),
    'depth_mm': fields.Float(description='Depth in millimeters'),
    'depth_percent': fields.Float(description='Depth percentage'),
    'table_percent': fields.Float(description='Table percentage'),
    'girdle': fields.String(description='Girdle description'),
    'culet': fields.String(description='Culet description'),

    # Certification
    'certificate_no': fields.String(required=True),
    'certification_lab': fields.String(description='Certification laboratory'),
    'certificate_date': fields.String(description='Certificate date'),
    'certificate_url': fields.String(description='Certificate URL'),

    # Pricing
    'cost_price': fields.Float(description='Purchase cost'),
    'retail_price': fields.Float(description='Retail price'),
    'market_value': fields.Float(description='Current market value'),
    'last_valuation_date': fields.String(description='Last valuation date'),

    # Inventory
    'quantity': fields.Integer(required=True),
    'reserved_quantity': fields.Integer(description='Reserved quantity'),
    'available_quantity': fields.Integer(description='Available quantity'),
    'minimum_stock': fields.Integer(description='Minimum stock level'),

    # Status and Location
    'status': fields.String(description='Diamond status'),
    'location': fields.String(description='Storage location'),
    'notes': fields.String(description='Additional notes'),

    # Relationships
    'vendor_id': fields.Integer(required=False),
    'vendorName': fields.String(required=False),

    # Dates
    'purchase_date': fields.String,
    'created_at': fields.String(readOnly=True),
    'updated_at': fields.String(readOnly=True),

    # Calculated Fields
    'profit_margin': fields.Float(readOnly=True, description='Profit margin percentage'),
    'profit_amount': fields.Float(readOnly=True, description='Profit amount'),
    'is_low_stock': fields.Boolean(readOnly=True, description='Is below minimum stock'),

    # Legacy
    'size_mm': fields.String(description='Legacy size field')
})

error_model = diamond_ns.model('DiamondError', {
    'status': fields.String(description='Error status'),
    'message': fields.String(description='Error message'),
    'status_code': fields.Integer(description='HTTP status code')
})

diamond_update_model = diamond_ns.model('DiamondUpdate', {
    'shape_id': fields.Integer(required=False),
    'carat': fields.Float(required=False),
    'color': fields.String(required=False),
    'clarity': fields.String(required=False),
    'cut_grade': fields.String(required=False),
    'polish': fields.String(required=False),
    'symmetry': fields.String(required=False),
    'fluorescence': fields.String(required=False),
    'fluorescence_color': fields.String(required=False),
    'length_mm': fields.Float(required=False),
    'width_mm': fields.Float(required=False),
    'depth_mm': fields.Float(required=False),
    'depth_percent': fields.Float(required=False),
    'table_percent': fields.Float(required=False),
    'girdle': fields.String(required=False),
    'culet': fields.String(required=False),
    'certificate_no': fields.String(required=False),
    'certification_lab': fields.String(required=False),
    'certificate_date': fields.String(required=False),
    'certificate_url': fields.String(required=False),
    'cost_price': fields.Float(required=False),
    'retail_price': fields.Float(required=False),
    'market_value': fields.Float(required=False),
    'last_valuation_date': fields.String(required=False),
    'quantity': fields.Integer(required=False),
    'reserved_quantity': fields.Integer(required=False),
    'minimum_stock': fields.Integer(required=False),
    'status': fields.String(required=False),
    'location': fields.String(required=False),
    'notes': fields.String(required=False),
    'vendor_id': fields.Integer(required=False),
    'purchase_date': fields.String(required=False),
    'size_mm': fields.String(required=False)  # Legacy field
})

shape_model = diamond_ns.model('Shape', {
    'id': fields.Integer(readOnly=True),
    'name': fields.String(required=True)
})

def diamond_to_dict(d):
    """Convert diamond model to dictionary with optimized field access."""
    return {
        'id': d.id,
        'shape': d.shape.name if d.shape else None,
        'shape_id': d.shape_id,
        'carat': d.carat,
        'color': d.color,
        'clarity': d.clarity,
        'cut_grade': d.cut_grade,
        'polish': d.polish,
        'symmetry': d.symmetry,
        'fluorescence': d.fluorescence,
        'fluorescence_color': d.fluorescence_color,
        'length_mm': d.length_mm,
        'width_mm': d.width_mm,
        'depth_mm': d.depth_mm,
        'depth_percent': d.depth_percent,
        'table_percent': d.table_percent,
        'girdle': d.girdle,
        'culet': d.culet,
        'certificate_no': d.certificate_no,
        'certification_lab': d.certification_lab,
        'certificate_date': d.certificate_date.isoformat() if d.certificate_date else None,
        'certificate_url': d.certificate_url,
        'cost_price': d.cost_price,
        'retail_price': d.retail_price,
        'market_value': d.market_value,
        'last_valuation_date': d.last_valuation_date.isoformat() if d.last_valuation_date else None,
        'quantity': d.quantity,
        'reserved_quantity': d.reserved_quantity,
        'available_quantity': d.available_quantity,
        'minimum_stock': d.minimum_stock,
        'status': d.status,
        'location': d.location,
        'notes': d.notes,
        'vendor_id': d.vendor_id,
        'vendorName': d.vendor.name if d.vendor else None,
        'purchase_date': d.purchase_date.isoformat() if d.purchase_date else None,
        'created_at': d.created_at.isoformat() if d.created_at else None,
        'updated_at': d.updated_at.isoformat() if d.updated_at else None,
        'size_mm': d.size_mm,  # Legacy field
        'profit_margin': d.get_profit_margin() if hasattr(d, 'get_profit_margin') else None,
        'profit_amount': d.get_profit_amount() if hasattr(d, 'get_profit_amount') else None,
        'is_low_stock': d.is_low_stock() if hasattr(d, 'is_low_stock') else None
    }

# Standardized error handling decorator
def handle_errors(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except IntegrityError:
            db.session.rollback()
            diamond_ns.abort(400, 'Database integrity error occurred.')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')
    return wrapper

# Setup audit logger
logger = logging.getLogger('diamond_audit')
if not logger.handlers:
    handler = logging.FileHandler('diamond_audit.log')
    formatter = logging.Formatter('%(asctime)s %(levelname)s %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

def validate_diamond_fields(data, is_update=False):
    """Enhanced validation for diamond fields with industry standards."""
    errors = []

    # Required fields for create
    required_fields = ['shape_id', 'carat', 'clarity', 'color', 'certificate_no']
    if not is_update:
        for field in required_fields:
            if field not in data or data[field] in [None, '']:
                errors.append(f"Missing or empty required field: {field}")

    # Carat validation
    carat = data.get('carat')
    if carat is not None:
        try:
            carat = float(carat)
            if not (0.01 <= carat <= 100):
                errors.append('Carat must be between 0.01 and 100')
        except (TypeError, ValueError):
            errors.append('Carat must be a number')

    # Color validation (GIA standard)
    color = data.get('color')
    if color and not Diamond.validate_color(color.upper()):
        errors.append(f'Color must be one of: {", ".join(Diamond.VALID_COLORS)}')

    # Clarity validation (GIA standard)
    clarity = data.get('clarity')
    if clarity and not Diamond.validate_clarity(clarity.upper()):
        errors.append(f'Clarity must be one of: {", ".join(Diamond.VALID_CLARITIES)}')

    # Cut grade validation
    cut_grade = data.get('cut_grade')
    if cut_grade and not Diamond.validate_cut_grade(cut_grade):
        errors.append(f'Cut grade must be one of: {", ".join(Diamond.VALID_CUT_GRADES)}')

    # Polish validation
    polish = data.get('polish')
    if polish and polish not in Diamond.VALID_POLISH_SYMMETRY:
        errors.append(f'Polish must be one of: {", ".join(Diamond.VALID_POLISH_SYMMETRY)}')

    # Symmetry validation
    symmetry = data.get('symmetry')
    if symmetry and symmetry not in Diamond.VALID_POLISH_SYMMETRY:
        errors.append(f'Symmetry must be one of: {", ".join(Diamond.VALID_POLISH_SYMMETRY)}')

    # Fluorescence validation
    fluorescence = data.get('fluorescence')
    if fluorescence and not Diamond.validate_fluorescence(fluorescence):
        errors.append(f'Fluorescence must be one of: {", ".join(Diamond.VALID_FLUORESCENCE)}')

    # Certification lab validation
    cert_lab = data.get('certification_lab')
    if cert_lab and not Diamond.validate_certification_lab(cert_lab):
        errors.append(f'Certification lab must be one of: {", ".join(Diamond.VALID_CERTIFICATION_LABS)}')

    # Certificate number validation
    cert = data.get('certificate_no', '')
    if cert and not re.match(r'^[a-zA-Z0-9\-]{3,50}$', cert):
        errors.append('Certificate number must be 3-50 alphanumeric characters')

    # Measurement validations
    for field in ['length_mm', 'width_mm', 'depth_mm']:
        value = data.get(field)
        if value is not None:
            try:
                value = float(value)
                if not (0.1 <= value <= 50):
                    errors.append(f'{field} must be between 0.1 and 50 mm')
            except (TypeError, ValueError):
                errors.append(f'{field} must be a number')

    # Percentage validations
    for field in ['depth_percent', 'table_percent']:
        value = data.get(field)
        if value is not None:
            try:
                value = float(value)
                if not (1 <= value <= 100):
                    errors.append(f'{field} must be between 1 and 100 percent')
            except (TypeError, ValueError):
                errors.append(f'{field} must be a number')

    # Price validations
    for field in ['cost_price', 'retail_price', 'market_value']:
        value = data.get(field)
        if value is not None:
            try:
                value = float(value)
                if value < 0:
                    errors.append(f'{field} must be non-negative')
            except (TypeError, ValueError):
                errors.append(f'{field} must be a number')

    # Quantity validations
    for field in ['quantity', 'reserved_quantity', 'minimum_stock']:
        value = data.get(field)
        if value is not None:
            try:
                value = int(value)
                if value < 0:
                    errors.append(f'{field} must be non-negative')
            except (TypeError, ValueError):
                errors.append(f'{field} must be an integer')

    # Status validation
    status = data.get('status')
    if status and status not in Diamond.VALID_STATUSES:
        errors.append(f'Status must be one of: {", ".join(Diamond.VALID_STATUSES)}')

    # Business rule validations
    # Price consistency checks
    cost_price = data.get('cost_price')
    retail_price = data.get('retail_price')
    market_value = data.get('market_value')

    if cost_price is not None and retail_price is not None:
        try:
            cost_price = float(cost_price)
            retail_price = float(retail_price)
            if cost_price > retail_price:
                errors.append('Cost price should not exceed retail price')
            # Check for reasonable profit margin (warn if margin is too low or too high)
            if retail_price > 0:
                margin = ((retail_price - cost_price) / cost_price) * 100
                if margin < 5:
                    errors.append('Warning: Profit margin is very low (less than 5%)')
                elif margin > 500:
                    errors.append('Warning: Profit margin is very high (more than 500%)')
        except (TypeError, ValueError):
            pass  # Price validation already handled above

    # Quantity consistency checks
    quantity = data.get('quantity')
    reserved_quantity = data.get('reserved_quantity')
    minimum_stock = data.get('minimum_stock')

    if quantity is not None and reserved_quantity is not None:
        try:
            quantity = int(quantity)
            reserved_quantity = int(reserved_quantity)
            if reserved_quantity > quantity:
                errors.append('Reserved quantity cannot exceed total quantity')
        except (TypeError, ValueError):
            pass  # Quantity validation already handled above

    if quantity is not None and minimum_stock is not None:
        try:
            quantity = int(quantity)
            minimum_stock = int(minimum_stock)
            if minimum_stock > quantity:
                errors.append('Minimum stock should not exceed current quantity')
        except (TypeError, ValueError):
            pass

    # Measurement consistency checks
    length_mm = data.get('length_mm')
    width_mm = data.get('width_mm')
    depth_mm = data.get('depth_mm')

    if all(x is not None for x in [length_mm, width_mm, depth_mm]):
        try:
            length_mm = float(length_mm)
            width_mm = float(width_mm)
            depth_mm = float(depth_mm)

            # Check for reasonable proportions
            if length_mm > 0 and width_mm > 0:
                ratio = max(length_mm, width_mm) / min(length_mm, width_mm)
                if ratio > 3:
                    errors.append('Warning: Length to width ratio seems unusual (>3:1)')

            if depth_mm > max(length_mm, width_mm):
                errors.append('Warning: Depth exceeds length/width - please verify measurements')
        except (TypeError, ValueError):
            pass

    # Certificate number format validation (enhanced)
    cert_no = data.get('certificate_no', '')
    if cert_no:
        # Check for common certificate number patterns
        if len(cert_no) < 3:
            errors.append('Certificate number is too short (minimum 3 characters)')
        elif len(cert_no) > 50:
            errors.append('Certificate number is too long (maximum 50 characters)')

        # Check for suspicious patterns
        if cert_no.lower() in ['test', 'sample', 'temp', 'dummy']:
            errors.append('Certificate number appears to be a placeholder')

    return errors

# Diamond list operations
@diamond_ns.route('/diamonds')
class DiamondList(Resource):
    @diamond_ns.doc('list_diamonds')
    # Removed marshal_list_with because we return a dict, not a list
    @diamond_ns.response(200, 'List of diamonds', [diamond_model])
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get all diamonds with comprehensive professional filtering."""
        # Use eager loading to prevent N+1 queries
        query = Diamond.query.options(
            joinedload(Diamond.shape),
            joinedload(Diamond.vendor)
        )

        # Basic filters
        for field in ['color', 'clarity', 'status', 'cut_grade', 'polish', 'symmetry',
                     'fluorescence', 'certification_lab', 'location']:
            value = request.args.get(field)
            if value:
                query = query.filter(getattr(Diamond, field) == value)

        # Handle shape filtering by shape_id or shape name
        shape_filter = request.args.get('shape')
        if shape_filter:
            if shape_filter.isdigit():
                query = query.filter(Diamond.shape_id == int(shape_filter))
            else:
                query = query.join(Shape).filter(Shape.name.ilike(f"%{shape_filter}%"))

        # Enhanced search across multiple fields
        search = request.args.get('search')
        if search:
            search = search.strip()
            query = query.join(Shape).filter(
                (Shape.name.ilike(f"%{search}%")) |
                (Diamond.clarity.ilike(f"%{search}%")) |
                (Diamond.color.ilike(f"%{search}%")) |
                (Diamond.certificate_no.ilike(f"%{search}%")) |
                (Diamond.notes.ilike(f"%{search}%")) |
                (Diamond.location.ilike(f"%{search}%"))
            )

        # Certificate number exact search
        certificate_no = request.args.get('certificate_no')
        if certificate_no:
            query = query.filter(Diamond.certificate_no.ilike(f"%{certificate_no}%"))

        # Carat range filtering
        min_carat = request.args.get('min_carat', type=float)
        max_carat = request.args.get('max_carat', type=float)
        if min_carat is not None:
            query = query.filter(Diamond.carat >= min_carat)
        if max_carat is not None:
            query = query.filter(Diamond.carat <= max_carat)

        # Price range filtering
        min_price = request.args.get('min_price', type=float)
        max_price = request.args.get('max_price', type=float)
        if min_price is not None:
            query = query.filter(Diamond.retail_price >= min_price)
        if max_price is not None:
            query = query.filter(Diamond.retail_price <= max_price)

        # Cost price range filtering
        min_cost_price = request.args.get('min_cost_price', type=float)
        max_cost_price = request.args.get('max_cost_price', type=float)
        if min_cost_price is not None:
            query = query.filter(Diamond.cost_price >= min_cost_price)
        if max_cost_price is not None:
            query = query.filter(Diamond.cost_price <= max_cost_price)

        # Quantity filtering
        min_quantity = request.args.get('min_quantity', type=int)
        if min_quantity is not None:
            query = query.filter(Diamond.quantity >= min_quantity)

        # Low stock filter
        low_stock_only = request.args.get('low_stock_only')
        if low_stock_only == 'true':
            query = query.filter(Diamond.quantity <= Diamond.minimum_stock)

        # Vendor filtering
        vendor_id = request.args.get('vendor_id', type=int)
        if vendor_id:
            query = query.filter(Diamond.vendor_id == vendor_id)

        # Enhanced sorting options
        sort_by = request.args.get('sort_by')
        sort_dir = request.args.get('sort_dir', 'asc')
        valid_sort_fields = [
            'carat', 'quantity', 'purchase_date', 'created_at', 'updated_at',
            'retail_price', 'cost_price', 'market_value', 'color', 'clarity',
            'cut_grade', 'certificate_no'
        ]

        if sort_by in valid_sort_fields:
            sort_col = getattr(Diamond, sort_by)
            if sort_dir == 'desc':
                sort_col = sort_col.desc()
            else:
                sort_col = sort_col.asc()
            query = query.order_by(sort_col)
        else:
            # Default sorting by created_at desc
            query = query.order_by(Diamond.created_at.desc())

        # Pagination
        page = request.args.get('page', default=1, type=int)
        limit = request.args.get('limit', default=20, type=int)

        # Ensure reasonable limits
        limit = min(limit, 100)  # Max 100 items per page

        pagination = query.paginate(page=page, per_page=limit, error_out=False)
        diamonds = pagination.items

        return {
            'data': [diamond_to_dict(d) for d in diamonds],
            'total': pagination.total,
            'page': page,
            'limit': limit,
            'pages': pagination.pages
        }, 200

# CSV Export endpoint
@diamond_ns.route('/diamonds/export')
@diamond_ns.route('/diamonds/export/csv')  # Add alternative route for compatibility
class DiamondExport(Resource):
    @diamond_ns.doc('export_diamonds_csv')
    @diamond_ns.response(200, 'CSV file with diamonds data')
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Export diamonds to CSV with all filtering options."""
        try:
            # Use the same filtering logic as DiamondList
            query = Diamond.query.options(
                joinedload(Diamond.shape),
                joinedload(Diamond.vendor)
            )

            # Apply all the same filters as the main list endpoint
            # Basic filters
            for field in ['color', 'clarity', 'status', 'cut_grade', 'polish', 'symmetry',
                         'fluorescence', 'certification_lab', 'location']:
                value = request.args.get(field)
                if value:
                    query = query.filter(getattr(Diamond, field) == value)

            # Handle shape filtering
            shape_filter = request.args.get('shape')
            if shape_filter:
                if shape_filter.isdigit():
                    query = query.filter(Diamond.shape_id == int(shape_filter))
                else:
                    query = query.join(Shape).filter(Shape.name.ilike(f"%{shape_filter}%"))

            # Enhanced search
            search = request.args.get('search')
            if search:
                search = search.strip()
                query = query.join(Shape).filter(
                    (Shape.name.ilike(f"%{search}%")) |
                    (Diamond.clarity.ilike(f"%{search}%")) |
                    (Diamond.color.ilike(f"%{search}%")) |
                    (Diamond.certificate_no.ilike(f"%{search}%")) |
                    (Diamond.notes.ilike(f"%{search}%")) |
                    (Diamond.location.ilike(f"%{search}%"))
                )

            # Certificate number search
            certificate_no = request.args.get('certificate_no')
            if certificate_no:
                query = query.filter(Diamond.certificate_no.ilike(f"%{certificate_no}%"))

            # Carat range filtering
            min_carat = request.args.get('min_carat', type=float)
            max_carat = request.args.get('max_carat', type=float)
            if min_carat is not None:
                query = query.filter(Diamond.carat >= min_carat)
            if max_carat is not None:
                query = query.filter(Diamond.carat <= max_carat)

            # Price range filtering
            min_price = request.args.get('min_price', type=float)
            max_price = request.args.get('max_price', type=float)
            if min_price is not None:
                query = query.filter(Diamond.retail_price >= min_price)
            if max_price is not None:
                query = query.filter(Diamond.retail_price <= max_price)

            # Cost price range filtering
            min_cost_price = request.args.get('min_cost_price', type=float)
            max_cost_price = request.args.get('max_cost_price', type=float)
            if min_cost_price is not None:
                query = query.filter(Diamond.cost_price >= min_cost_price)
            if max_cost_price is not None:
                query = query.filter(Diamond.cost_price <= max_cost_price)

            # Quantity filtering
            min_quantity = request.args.get('min_quantity', type=int)
            if min_quantity is not None:
                query = query.filter(Diamond.quantity >= min_quantity)

            # Low stock filter
            low_stock_only = request.args.get('low_stock_only')
            if low_stock_only == 'true':
                query = query.filter(Diamond.quantity <= Diamond.minimum_stock)

            # Vendor filtering
            vendor_id = request.args.get('vendor_id', type=int)
            if vendor_id:
                query = query.filter(Diamond.vendor_id == vendor_id)

            # Get all diamonds (no pagination for export)
            diamonds = query.all()

            # Create CSV content
            output = io.StringIO()
            writer = csv.writer(output)

            # Comprehensive CSV headers
            headers = [
                'ID', 'Shape', 'Carat', 'Color', 'Clarity', 'Cut Grade',
                'Polish', 'Symmetry', 'Fluorescence', 'Fluorescence Color',
                'Length (mm)', 'Width (mm)', 'Depth (mm)', 'Depth %', 'Table %',
                'Girdle', 'Culet', 'Certificate No.', 'Certification Lab',
                'Certificate Date', 'Certificate URL', 'Cost Price', 'Retail Price',
                'Market Value', 'Last Valuation Date', 'Quantity', 'Reserved Quantity',
                'Available Quantity', 'Minimum Stock', 'Status', 'Location',
                'Vendor', 'Purchase Date', 'Notes', 'Created At', 'Updated At'
            ]
            writer.writerow(headers)

            # Write diamond data
            for diamond in diamonds:
                row = [
                    diamond.id,
                    diamond.shape.name if diamond.shape else '',
                    diamond.carat,
                    diamond.color,
                    diamond.clarity,
                    diamond.cut_grade or '',
                    diamond.polish or '',
                    diamond.symmetry or '',
                    diamond.fluorescence or '',
                    diamond.fluorescence_color or '',
                    diamond.length_mm or '',
                    diamond.width_mm or '',
                    diamond.depth_mm or '',
                    diamond.depth_percent or '',
                    diamond.table_percent or '',
                    diamond.girdle or '',
                    diamond.culet or '',
                    diamond.certificate_no,
                    diamond.certification_lab or '',
                    diamond.certificate_date.strftime('%Y-%m-%d') if diamond.certificate_date else '',
                    diamond.certificate_url or '',
                    diamond.cost_price or '',
                    diamond.retail_price or '',
                    diamond.market_value or '',
                    diamond.last_valuation_date.strftime('%Y-%m-%d') if diamond.last_valuation_date else '',
                    diamond.quantity,
                    diamond.reserved_quantity or 0,
                    diamond.available_quantity or diamond.quantity,
                    diamond.minimum_stock or 1,
                    diamond.status,
                    diamond.location or '',
                    diamond.vendor.name if diamond.vendor else '',
                    diamond.purchase_date.strftime('%Y-%m-%d') if diamond.purchase_date else '',
                    diamond.notes or '',
                    diamond.created_at.strftime('%Y-%m-%d %H:%M:%S') if diamond.created_at else '',
                    diamond.updated_at.strftime('%Y-%m-%d %H:%M:%S') if diamond.updated_at else ''
                ]
                writer.writerow(row)

            # Create response
            output.seek(0)
            csv_content = output.getvalue()
            output.close()

            # Create filename with timestamp
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'diamonds_export_{timestamp}.csv'

            # Create response with proper headers
            response = make_response(csv_content)
            response.headers['Content-Type'] = 'text/csv'
            response.headers['Content-Disposition'] = f'attachment; filename={filename}'
            response.headers['Content-Length'] = len(csv_content)

            return response

        except Exception as e:
            logging.error(f"CSV export failed: {str(e)}")
            diamond_ns.abort(500, f'Export failed: {str(e)}')

# Bulk operations endpoints
@diamond_ns.route('/diamonds/bulk')
class DiamondBulkOperations(Resource):
    @diamond_ns.doc('bulk_update_diamonds')
    @diamond_ns.expect(diamond_ns.model('BulkUpdate', {
        'diamond_ids': fields.List(fields.Integer, required=True, description='List of diamond IDs to update'),
        'updates': fields.Raw(required=True, description='Fields to update')
    }))
    @diamond_ns.response(200, 'Bulk update successful')
    @diamond_ns.response(400, 'Invalid request', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def put(self):
        """Bulk update diamonds."""
        try:
            data = request.get_json()
            diamond_ids = data.get('diamond_ids', [])
            updates = data.get('updates', {})

            if not diamond_ids:
                diamond_ns.abort(400, 'No diamond IDs provided')

            if not updates:
                diamond_ns.abort(400, 'No updates provided')

            # Validate updates
            errors = validate_diamond_fields(updates, is_update=True)
            if errors:
                diamond_ns.abort(400, '; '.join(errors))

            # Get diamonds to update
            diamonds = Diamond.query.filter(Diamond.id.in_(diamond_ids)).all()

            if len(diamonds) != len(diamond_ids):
                found_ids = [d.id for d in diamonds]
                missing_ids = [id for id in diamond_ids if id not in found_ids]
                diamond_ns.abort(400, f'Diamonds not found: {missing_ids}')

            updated_count = 0

            # Apply updates to each diamond
            for diamond in diamonds:
                # Basic properties
                for field in ['status', 'location', 'notes', 'minimum_stock']:
                    if field in updates:
                        setattr(diamond, field, updates[field])

                # Pricing updates
                for field in ['cost_price', 'retail_price', 'market_value']:
                    if field in updates:
                        setattr(diamond, field, updates[field])

                # Date fields
                if 'last_valuation_date' in updates and updates['last_valuation_date']:
                    diamond.last_valuation_date = datetime.strptime(updates['last_valuation_date'], '%Y-%m-%d').date()

                # Vendor update
                if 'vendor_id' in updates:
                    if updates['vendor_id']:
                        vendor = Vendor.query.get(updates['vendor_id'])
                        if not vendor:
                            diamond_ns.abort(400, f"Vendor with id {updates['vendor_id']} does not exist.")
                    diamond.vendor_id = updates['vendor_id']

                updated_count += 1

            db.session.commit()

            return {
                'message': f'Successfully updated {updated_count} diamonds',
                'updated_count': updated_count
            }, 200

        except Exception as e:
            db.session.rollback()
            logging.error(f"Bulk update failed: {str(e)}")
            diamond_ns.abort(500, f'Bulk update failed: {str(e)}')

    @diamond_ns.doc('bulk_delete_diamonds')
    @diamond_ns.expect(diamond_ns.model('BulkDelete', {
        'diamond_ids': fields.List(fields.Integer, required=True, description='List of diamond IDs to delete')
    }))
    @diamond_ns.response(200, 'Bulk delete successful')
    @diamond_ns.response(400, 'Invalid request', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def delete(self):
        """Bulk delete diamonds."""
        try:
            data = request.get_json()
            diamond_ids = data.get('diamond_ids', [])

            if not diamond_ids:
                diamond_ns.abort(400, 'No diamond IDs provided')

            # Get diamonds to delete
            diamonds = Diamond.query.filter(Diamond.id.in_(diamond_ids)).all()

            if len(diamonds) != len(diamond_ids):
                found_ids = [d.id for d in diamonds]
                missing_ids = [id for id in diamond_ids if id not in found_ids]
                diamond_ns.abort(400, f'Diamonds not found: {missing_ids}')

            deleted_count = 0

            # Check for constraints before deleting
            for diamond in diamonds:
                # Check if diamond is assigned to manufacturing or jewelry
                if hasattr(diamond, 'manufacturing_assignments') and diamond.manufacturing_assignments:
                    diamond_ns.abort(400, f'Diamond {diamond.id} is assigned to manufacturing and cannot be deleted')

                if hasattr(diamond, 'jewelry_assignments') and diamond.jewelry_assignments:
                    diamond_ns.abort(400, f'Diamond {diamond.id} is assigned to jewelry and cannot be deleted')

            # Delete diamonds
            for diamond in diamonds:
                db.session.delete(diamond)
                deleted_count += 1

            db.session.commit()

            return {
                'message': f'Successfully deleted {deleted_count} diamonds',
                'deleted_count': deleted_count
            }, 200

        except Exception as e:
            db.session.rollback()
            logging.error(f"Bulk delete failed: {str(e)}")
            diamond_ns.abort(500, f'Bulk delete failed: {str(e)}')

# Barcode generation endpoints
@diamond_ns.route('/diamonds/<int:diamond_id>/qr-code')
class DiamondQRCode(Resource):
    @diamond_ns.doc('generate_diamond_qr_code')
    @diamond_ns.response(200, 'QR code generated successfully')
    @diamond_ns.response(404, 'Diamond not found', error_model)
    @token_required
    def get(self, diamond_id):
        """Generate QR code for a diamond."""
        try:
            diamond = Diamond.query.get(diamond_id)
            if not diamond:
                diamond_ns.abort(404, 'Diamond not found')

            from app.utils.barcode_generator import generate_diamond_qr_code

            qr_code = generate_diamond_qr_code(
                diamond_id=diamond.id,
                certificate_no=diamond.certificate_no
            )

            return {
                'qr_code': qr_code,
                'diamond_id': diamond_id,
                'certificate_no': diamond.certificate_no
            }, 200

        except Exception as e:
            logging.error(f"QR code generation failed: {str(e)}")
            diamond_ns.abort(500, f'QR code generation failed: {str(e)}')

@diamond_ns.route('/diamonds/<int:diamond_id>/label')
class DiamondLabel(Resource):
    @diamond_ns.doc('generate_diamond_label')
    @diamond_ns.response(200, 'Label generated successfully')
    @diamond_ns.response(404, 'Diamond not found', error_model)
    @token_required
    def get(self, diamond_id):
        """Generate printable label for a diamond."""
        try:
            diamond = Diamond.query.options(joinedload(Diamond.shape)).get(diamond_id)
            if not diamond:
                diamond_ns.abort(404, 'Diamond not found')

            from app.utils.barcode_generator import generate_diamond_label

            diamond_data = {
                'id': diamond.id,
                'shape': diamond.shape.name if diamond.shape else 'Unknown',
                'carat': diamond.carat,
                'color': diamond.color,
                'clarity': diamond.clarity,
                'certificate_no': diamond.certificate_no
            }

            include_qr = request.args.get('include_qr', 'true').lower() == 'true'

            label = generate_diamond_label(diamond_data, include_qr=include_qr)

            return {
                'label': label,
                'diamond_id': diamond_id,
                'include_qr': include_qr
            }, 200

        except Exception as e:
            logging.error(f"Label generation failed: {str(e)}")
            diamond_ns.abort(500, f'Label generation failed: {str(e)}')

    @diamond_ns.doc('create_diamond')
    @diamond_ns.expect(diamond_model, validate=False)
    @diamond_ns.response(201, 'Diamond created', diamond_model)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Create a new diamond."""
        try:
            data = request.json
            if not data:
                diamond_ns.abort(400, 'No data provided')

            # Validate fields
            errors = validate_diamond_fields(data)
            if errors:
                return {
                    'status': 'error',
                    'message': '; '.join(errors),
                    'status_code': 400
                }, 400
            # Convert `*` to `x` in `size_mm` if provided
            if 'size_mm' in data and data['size_mm'] and '*' in data['size_mm']:
                data['size_mm'] = data['size_mm'].replace('*', 'x')
            # Pre-check for duplicate certificate_no
            existing_diamond = Diamond.query.filter_by(certificate_no=data.get('certificate_no')).first()
            if existing_diamond:
                return {
                    'status': 'error',
                    'message': f"Diamond with certificate_no '{data['certificate_no']}' already exists.",
                    'status_code': 409
                }, 409
            # Vendor validation
            vendor_id = data.get('vendor_id')
            if vendor_id == '' or vendor_id is None:
                vendor_id = None
            vendor = None
            if vendor_id:
                vendor = Vendor.query.get(vendor_id)
                if not vendor:
                    return {
                        'status': 'error',
                        'message': f"Vendor with id {vendor_id} does not exist.",
                        'status_code': 400
                    }, 400
            # Create diamond with all fields
            d = Diamond(
                shape_id=data['shape_id'],
                size_mm=data.get('size_mm'),  # Optional legacy field
                carat=data['carat'],
                clarity=data['clarity'],
                color=data['color'],
                cut_grade=data.get('cut_grade'),
                polish=data.get('polish'),
                symmetry=data.get('symmetry'),
                fluorescence=data.get('fluorescence'),
                fluorescence_color=data.get('fluorescence_color'),
                length_mm=data.get('length_mm'),
                width_mm=data.get('width_mm'),
                depth_mm=data.get('depth_mm'),
                depth_percent=data.get('depth_percent'),
                table_percent=data.get('table_percent'),
                girdle=data.get('girdle'),
                culet=data.get('culet'),
                certificate_no=data['certificate_no'],
                certification_lab=data.get('certification_lab'),
                certificate_date=datetime.strptime(data['certificate_date'], '%Y-%m-%d').date() if data.get('certificate_date') else None,
                certificate_url=data.get('certificate_url'),
                cost_price=data.get('cost_price'),
                retail_price=data.get('retail_price'),
                market_value=data.get('market_value'),
                last_valuation_date=datetime.strptime(data['last_valuation_date'], '%Y-%m-%d').date() if data.get('last_valuation_date') else None,
                quantity=data.get('quantity', 1),
                reserved_quantity=data.get('reserved_quantity', 0),
                minimum_stock=data.get('minimum_stock', 1),
                status=data.get('status', 'in_stock'),
                location=data.get('location'),
                notes=data.get('notes'),
                vendor_id=data.get('vendor_id'),
                purchase_date=datetime.strptime(data['purchase_date'], '%Y-%m-%d').date() if 'purchase_date' in data else None
            )
            db.session.add(d)
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=CREATE diamond_id={d.id} cert={d.certificate_no}")
            return diamond_to_dict(d), 201
        except IntegrityError as e:
            db.session.rollback()
            return {
                'status': 'error',
                'message': 'Database integrity error occurred.',
                'status_code': 409
            }, 409
        except Exception as e:
            db.session.rollback()
            import traceback
            logging.error(f"Diamond creation failed: {str(e)}")
            logging.error(f"Traceback: {traceback.format_exc()}")
            logging.error(f"Request data: {data}")
            return {
                'status': 'error',
                'message': f'Internal server error: {str(e)}',
                'status_code': 500
            }, 500

# Consolidated Diamond CRUD operations
@diamond_ns.route('/diamonds/<int:diamond_id>')
class DiamondCRUD(Resource):
    @diamond_ns.doc('get_diamond')
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(200, 'Diamond details', diamond_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @diamond_ns.response(404, 'Diamond not found', error_model)
    @token_required
    def get(self, diamond_id):
        """Get diamond by ID."""
        d = Diamond.query.get_or_404(diamond_id)
        return diamond_to_dict(d), 200

    @diamond_ns.doc('update_diamond')
    @diamond_ns.expect(diamond_update_model, validate=True)
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def put(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        data = request.json
        try:
            errors = validate_diamond_fields(data, is_update=True)
            if errors:
                diamond_ns.abort(400, '; '.join(errors))

            # Basic properties
            for field in ['shape_id', 'size_mm', 'carat', 'clarity', 'color', 'certificate_no', 'quantity', 'status']:
                if field in data:
                    setattr(d, field, data[field])

            # 4Cs and additional grading
            for field in ['cut_grade', 'polish', 'symmetry', 'fluorescence', 'fluorescence_color']:
                if field in data:
                    setattr(d, field, data[field])

            # Measurements
            for field in ['length_mm', 'width_mm', 'depth_mm', 'depth_percent', 'table_percent', 'girdle', 'culet']:
                if field in data:
                    setattr(d, field, data[field])

            # Certification
            for field in ['certification_lab', 'certificate_url']:
                if field in data:
                    setattr(d, field, data[field])

            # Pricing
            for field in ['cost_price', 'retail_price', 'market_value']:
                if field in data:
                    setattr(d, field, data[field])

            # Inventory
            for field in ['reserved_quantity', 'minimum_stock', 'location', 'notes']:
                if field in data:
                    setattr(d, field, data[field])

            # Date fields
            if 'purchase_date' in data:
                d.purchase_date = datetime.strptime(data['purchase_date'], '%Y-%m-%d').date()
            if 'certificate_date' in data and data['certificate_date']:
                d.certificate_date = datetime.strptime(data['certificate_date'], '%Y-%m-%d').date()
            if 'last_valuation_date' in data and data['last_valuation_date']:
                d.last_valuation_date = datetime.strptime(data['last_valuation_date'], '%Y-%m-%d').date()

            # Vendor validation
            if 'vendor_id' in data:
                if data['vendor_id']:
                    vendor = Vendor.query.get(data['vendor_id'])
                    if not vendor:
                        diamond_ns.abort(400, f"Vendor with id {data['vendor_id']} does not exist.")
                d.vendor_id = data['vendor_id']

            # Update available quantity
            if hasattr(d, 'update_available_quantity'):
                d.update_available_quantity()
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=UPDATE diamond_id={d.id} cert={d.certificate_no}")
            return diamond_to_dict(d), 200
        except IntegrityError as e:
            db.session.rollback()
            diamond_ns.abort(409, 'Database integrity error occurred.')
        except ValueError as ve:
            db.session.rollback()
            diamond_ns.abort(400, f'Value error: {str(ve)}')
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(500, f'Internal server error: {str(e)}')

    @diamond_ns.doc('delete_diamond')
    @diamond_ns.response(200, 'Diamond deleted')
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @admin_required
    def delete(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        try:
            db.session.delete(d)
            db.session.commit()
            # Audit log
            logger.info(f"user=API action=DELETE diamond_id={d.id} cert={d.certificate_no}")
            return {'message': 'Diamond deleted'}, 200
        except Exception as e:
            db.session.rollback()
            diamond_ns.abort(400, f'An error occurred: {str(e)}')

@diamond_ns.route('/diamonds/<int:diamond_id>/deduct')
@diamond_ns.response(404, 'Diamond not found', error_model)
class DiamondDeduct(Resource):
    @diamond_ns.doc('deduct_diamond_quantity')
    @diamond_ns.expect(diamond_ns.model('DeductQuantity', {
        'quantity': fields.Integer(required=True)
    }), validate=True)
    @diamond_ns.marshal_with(diamond_model)
    @diamond_ns.response(400, 'Not enough quantity', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def patch(self, diamond_id):
        d = Diamond.query.get_or_404(diamond_id)
        data = request.json
        qty = data.get('quantity')

        if not isinstance(qty, int) or qty <= 0:
            diamond_ns.abort(400, 'Quantity must be a positive integer.')

        if d.quantity < qty:
            diamond_ns.abort(400, f'Not enough stock. Available: {d.quantity}, Requested: {qty}')
            
        d.quantity -= qty
        if d.quantity == 0:
            d.status = 'used'
        db.session.commit()
        return diamond_to_dict(d), 200

# Shape list operations
@diamond_ns.route('/shapes')
class ShapeList(Resource):
    @diamond_ns.doc('list_shapes')
    @diamond_ns.marshal_list_with(shape_model)
    @diamond_ns.response(200, 'List of shapes', [shape_model])
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def get(self):
        """Get all shapes."""
        shapes = Shape.query.all()
        return [{'id': s.id, 'name': s.name} for s in shapes]

    @diamond_ns.doc('create_shape')
    @diamond_ns.expect(shape_model, validate=True)
    @diamond_ns.marshal_with(shape_model, code=201)
    @diamond_ns.response(400, 'Validation error', error_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    def post(self):
        """Create a new shape."""
        data = request.json
        # Normalize name: trim and capitalize first letter
        name = data['name'].strip().capitalize()
        # Check for existing shape (case-insensitive)
        existing = Shape.query.filter(db.func.lower(Shape.name) == name.lower()).first()
        if existing:
            diamond_ns.abort(409, 'Shape name already exists.')
        shape = Shape(name=name)
        db.session.add(shape)
        db.session.commit()
        return {'id': shape.id, 'name': shape.name}, 201

# Consolidated Shape Management
@diamond_ns.route('/shapes/<int:shape_id>')
class ShapeCRUD(Resource):
    @diamond_ns.doc('get_shape')
    @diamond_ns.marshal_with(shape_model)
    @diamond_ns.response(200, 'Shape details', shape_model)
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @diamond_ns.response(404, 'Shape not found', error_model)
    @token_required
    def get(self, shape_id):
        """Get shape by ID."""
        shape = Shape.query.get_or_404(shape_id)
        return {'id': shape.id, 'name': shape.name}, 200

    @diamond_ns.doc('delete_shape')
    @diamond_ns.response(204, 'Shape deleted')
    @diamond_ns.response(401, 'Unauthorized', error_model)
    @token_required
    @admin_required
    def delete(self, shape_id):
        shape = Shape.query.get_or_404(shape_id)
        db.session.delete(shape)
        db.session.commit()
        return '', 204

@diamond_ns.route('/manufacturing-types')
class ManufacturingTypesList(Resource):
    @diamond_ns.doc('list_manufacturing_types')
    def get(self):
        """List all manufacturing process types"""
        manufacturing = Manufacturing.query.all()
        return [{'id': m.id, 'name': m.name} for m in manufacturing]

    @diamond_ns.doc('create_manufacturing_type')
    def post(self):
        """Create a new manufacturing process type"""
        data = request.json
        m = Manufacturing(name=data['name'])
        db.session.add(m)
        db.session.commit()
        return {'id': m.id, 'name': m.name}, 201

# Removed duplicate jewelry routes - these are handled by jewelry.py

@diamond_ns.route('/diamonds/<int:diamond_id>/assign-manufacturing/<int:manufacturing_id>')
class AssignManufacturing(Resource):
    @diamond_ns.doc('assign_manufacturing')
    def patch(self, diamond_id, manufacturing_id):
        """Assign a diamond to a manufacturing process"""
        diamond = Diamond.query.get_or_404(diamond_id)
        manufacturing = Manufacturing.query.get_or_404(manufacturing_id)
        diamond.manufacturing_id = manufacturing.id
        db.session.commit()
        return {'message': 'Diamond assigned to manufacturing'}, 200

@diamond_ns.route('/diamonds/<int:diamond_id>/assign-jewelry/<int:jewelry_id>')
class AssignJewelry(Resource):
    @diamond_ns.doc('assign_jewelry')
    def patch(self, diamond_id, jewelry_id):
        """Assign a diamond to a jewelry item"""
        diamond = Diamond.query.get_or_404(diamond_id)
        jewelry = Jewelry.query.get_or_404(jewelry_id)
        diamond.jewelry_id = jewelry.id
        db.session.commit()
        return {'message': 'Diamond assigned to jewelry'}, 200
