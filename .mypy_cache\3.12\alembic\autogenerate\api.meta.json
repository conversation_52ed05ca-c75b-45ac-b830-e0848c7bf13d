{"data_mtime": 1753035443, "dep_lines": [16, 17, 19, 20, 29, 37, 41, 42, 44, 16, 18, 19, 26, 33, 1, 3, 4, 14, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 25, 25, 25, 25, 25, 20, 10, 20, 25, 25, 5, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.autogenerate.compare", "alembic.autogenerate.render", "alembic.operations.ops", "alembic.util.sqla_compat", "sqlalchemy.sql.schema", "alembic.runtime.environment", "alembic.runtime.migration", "alembic.script.base", "alembic.script.revision", "alembic.autogenerate", "alembic.util", "alembic.operations", "sqlalchemy.engine", "alembic.config", "__future__", "contextlib", "typing", "sqlalchemy", "alembic", "builtins", "_frozen_importlib", "abc", "alembic.runtime", "alembic.script", "alembic.util.exc", "enum", "os", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.reflection", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.elements", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "318a79319e476015798cc448796f1775594bb227", "id": "alembic.autogenerate.api", "ignore_all": true, "interface_hash": "1216dd7cf76e23b95499ec450f06f8866a47deeb", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\autogenerate\\api.py", "plugin_data": null, "size": 22219, "suppressed": [], "version_id": "1.15.0"}