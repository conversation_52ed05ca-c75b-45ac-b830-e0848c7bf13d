{".class": "MypyFile", "_fullname": "app.api.jewelry", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Diamond": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Diamond", "kind": "Gdef"}, "JewelryDeductDiamonds": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.jewelry.JewelryDeductDiamonds", "name": "JewelryDeductDiamonds", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.jewelry.JewelryDeductDiamonds", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.jewelry", "mro": ["app.api.jewelry.JewelryDeductDiamonds", "builtins.object"], "names": {".class": "SymbolTable", "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "jewelry_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryDeductDiamonds.patch", "name": "patch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryDeductDiamonds.patch", "name": "patch", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.jewelry.JewelryDeductDiamonds.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.jewelry.JewelryDeductDiamonds", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JewelryImageUpload": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.jewelry.JewelryImageUpload", "name": "JewelryImageUpload", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.jewelry.JewelryImageUpload", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.jewelry", "mro": ["app.api.jewelry.JewelryImageUpload", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "jewelry_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryImageUpload.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryImageUpload.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.jewelry.JewelryImageUpload.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.jewelry.JewelryImageUpload", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JewelryItem": {".class": "SymbolTableNode", "cross_ref": "app.models.jewelry.JewelryItem", "kind": "Gdef"}, "JewelryList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.jewelry.JewelryList", "name": "JewelryList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.jewelry.JewelryList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.jewelry", "mro": ["app.api.jewelry.JewelryList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryList.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryList.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.jewelry.JewelryList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.jewelry.JewelryList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JewelryMarkSold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.jewelry.JewelryMarkSold", "name": "JewelryMarkSold", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.jewelry.JewelryMarkSold", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.jewelry", "mro": ["app.api.jewelry.JewelryMarkSold", "builtins.object"], "names": {".class": "SymbolTable", "patch": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "jewelry_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryMarkSold.patch", "name": "patch", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryMarkSold.patch", "name": "patch", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.jewelry.JewelryMarkSold.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.jewelry.JewelryMarkSold", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JewelryResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.jewelry.JewelryResource", "name": "JewelryResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.jewelry.JewelryResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.jewelry", "mro": ["app.api.jewelry.JewelryResource", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "jewelry_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryResource.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryResource.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "jewelry_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryResource.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryResource.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "jewelry_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.jewelry.JewelryResource.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.JewelryResource.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.jewelry.JewelryResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.jewelry.JewelryResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Resource", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.jewelry.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.jewelry.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.jewelry.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.jewelry.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.jewelry.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.jewelry.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "create_jewelry_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.jewelry.create_jewelry_model", "name": "create_jewelry_model", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "diamond_link_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.jewelry.diamond_link_model", "name": "diamond_link_model", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.jewelry.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.fields", "source_any": null, "type_of_any": 3}}}, "handle_errors": {".class": "SymbolTableNode", "cross_ref": "app.utils.error_handler.handle_errors", "kind": "Gdef"}, "jewelry_diamonds": {".class": "SymbolTableNode", "cross_ref": "app.models.jewelry.jewelry_diamonds", "kind": "Gdef"}, "jewelry_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.jewelry.jewelry_model", "name": "jewelry_model", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "jewelry_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.jewelry.jewelry_ns", "name": "jewelry_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "jewelry_to_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["i"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.jewelry.jewelry_to_dict", "name": "jewelry_to_dict", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "reqparse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.jewelry.reqparse", "name": "reqparse", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.reqparse", "source_any": null, "type_of_any": 3}}}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "secure_filename": {".class": "SymbolTableNode", "cross_ref": "werkzeug.utils.secure_filename", "kind": "Gdef"}, "token_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.token_required", "kind": "Gdef"}, "upload_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.jewelry.upload_parser", "name": "upload_parser", "type": {".class": "AnyType", "missing_import_name": "app.api.jewelry.reqparse", "source_any": {".class": "AnyType", "missing_import_name": "app.api.jewelry.reqparse", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\jewelry.py"}