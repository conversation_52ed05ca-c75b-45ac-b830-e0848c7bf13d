{"data_mtime": 1753035428, "dep_lines": [494, 497, 493, 494, 484, 486, 493, 1, 1, 1], "dep_prios": [10, 25, 10, 20, 5, 5, 20, 5, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.sql.compiler", "sqlalchemy.exc", "sqlalchemy.sql", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc"], "hash": "40edb7b361d02322136df99d8e88b4520a1fe49b", "id": "sqlalchemy.ext.compiler", "ignore_all": true, "interface_hash": "b5d88a69b8d1fb4ac2cbdebc0df5e5757064b40a", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\ext\\compiler.py", "plugin_data": null, "size": 21489, "suppressed": [], "version_id": "1.15.0"}