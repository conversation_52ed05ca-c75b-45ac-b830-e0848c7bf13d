{"data_mtime": 1753035443, "dep_lines": [1, 2, 4, 17, 25, 29, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["alembic.util.editor", "alembic.util.exc", "alembic.util.langhelpers", "alembic.util.messaging", "alembic.util.pyfiles", "alembic.util.sqla_compat", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "3d5164feff68c0d4c9122ceed15c4601fe48f107", "id": "alembic.util", "ignore_all": true, "interface_hash": "4f53ff23fe32668977a23d7945a0a0575a475874", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\util\\__init__.py", "plugin_data": null, "size": 1519, "suppressed": [], "version_id": "1.15.0"}