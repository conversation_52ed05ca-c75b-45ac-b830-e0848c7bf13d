{"data_mtime": 1753088442, "dep_lines": [36, 40, 40, 41, 42, 49, 49, 32, 34, 35, 37, 38, 40, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 25, 20, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "PIL.Image", "PIL.ImageColor", "PIL._deprecate", "PIL._typing", "PIL.ImageDraw2", "PIL.ImageFont", "__future__", "math", "struct", "types", "typing", "PIL", "builtins", "PIL.ImagePalette", "PIL._imaging", "_frozen_importlib", "abc"], "hash": "4d9462f896dd28a0e093c9b4c146fdea6382e880", "id": "PIL.ImageDraw", "ignore_all": true, "interface_hash": "e2d7f15370c2871d215d2357827fcecb9f8ae799", "mtime": 1750655007, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\PIL\\ImageDraw.py", "plugin_data": null, "size": 43600, "suppressed": [], "version_id": "1.15.0"}