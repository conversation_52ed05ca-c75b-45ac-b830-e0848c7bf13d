import React, { useState } from 'react';
import { Upload, X, Star, Image as ImageIcon } from 'lucide-react';
import Button from './ui/Button';
import Modal from './ui/Modal';

interface DiamondImage {
  id: number;
  diamond_id: number;
  image_url: string;
  image_type: string;
  is_primary: boolean;
  alt_text?: string;
  file_size?: number;
  file_format?: string;
  width?: number;
  height?: number;
  uploaded_at: string;
}

interface DiamondImageGalleryProps {
  diamondId: number;
  images: DiamondImage[];
  onImageUpload?: (file: File, imageType: string, isPrimary: boolean) => Promise<void>;
  onImageDelete?: (imageId: number) => Promise<void>;
  onImageUpdate?: (imageId: number, updates: Partial<DiamondImage>) => Promise<void>;
  isLoading?: boolean;
  className?: string;
}

const DiamondImageGallery: React.FC<DiamondImageGalleryProps> = ({
  diamondId,
  images,
  onImageUpload,
  onImageDelete,
  onImageUpdate,
  isLoading = false,
  className = ''
}) => {
  const [selectedImage, setSelectedImage] = useState<DiamondImage | null>(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [uploadType, setUploadType] = useState('main');
  const [uploadIsPrimary, setUploadIsPrimary] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  const imageTypes = [
    { value: 'main', label: 'Main Photo' },
    { value: 'side', label: 'Side View' },
    { value: 'certificate', label: 'Certificate' },
    { value: '360', label: '360° View' },
    { value: 'grading_report', label: 'Grading Report' },
    { value: 'laser_inscription', label: 'Laser Inscription' }
  ];

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadFile(file);
    }
  };

  const handleUpload = async () => {
    if (!uploadFile || !onImageUpload) return;

    setIsUploading(true);
    try {
      await onImageUpload(uploadFile, uploadType, uploadIsPrimary);
      setIsUploadModalOpen(false);
      setUploadFile(null);
      setUploadType('main');
      setUploadIsPrimary(false);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setIsUploading(false);
    }
  };

  const handleSetPrimary = async (imageId: number) => {
    if (!onImageUpdate) return;
    
    try {
      await onImageUpdate(imageId, { is_primary: true });
    } catch (error) {
      console.error('Failed to set primary image:', error);
    }
  };

  const handleDelete = async (imageId: number) => {
    if (!onImageDelete) return;
    
    if (window.confirm('Are you sure you want to delete this image?')) {
      try {
        await onImageDelete(imageId);
      } catch (error) {
        console.error('Failed to delete image:', error);
      }
    }
  };

  const primaryImage = images.find(img => img.is_primary);
  const otherImages = images.filter(img => !img.is_primary);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Diamond Images ({images.length})
        </h3>
        {onImageUpload && (
          <Button
            size="sm"
            onClick={() => setIsUploadModalOpen(true)}
            disabled={isLoading}
          >
            <Upload className="h-4 w-4 mr-2" />
            Upload Image
          </Button>
        )}
      </div>

      {/* Images Grid */}
      {images.length === 0 ? (
        <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
          <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-500">No images uploaded yet</p>
          {onImageUpload && (
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setIsUploadModalOpen(true)}
              className="mt-2"
            >
              Upload First Image
            </Button>
          )}
        </div>
      ) : (
        <div className="space-y-4">
          {/* Primary Image */}
          {primaryImage && (
            <div className="relative">
              <div className="relative group">
                <img
                  src={primaryImage.image_url}
                  alt={primaryImage.alt_text || 'Diamond image'}
                  className="w-full h-64 object-cover rounded-lg cursor-pointer"
                  onClick={() => setSelectedImage(primaryImage)}
                />
                <div className="absolute top-2 left-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    <Star className="h-3 w-3 mr-1" />
                    Primary
                  </span>
                </div>
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <div className="flex space-x-1">
                    {onImageDelete && (
                      <Button
                        size="sm"
                        variant="danger"
                        onClick={() => handleDelete(primaryImage.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
                <div className="absolute bottom-2 left-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-black bg-opacity-50 text-white">
                    {primaryImage.image_type}
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Other Images */}
          {otherImages.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {otherImages.map((image) => (
                <div key={image.id} className="relative group">
                  <img
                    src={image.image_url}
                    alt={image.alt_text || 'Diamond image'}
                    className="w-full h-32 object-cover rounded-lg cursor-pointer"
                    onClick={() => setSelectedImage(image)}
                  />
                  <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <div className="flex space-x-1">
                      {onImageUpdate && (
                        <Button
                          size="sm"
                          variant="secondary"
                          onClick={() => handleSetPrimary(image.id)}
                          title="Set as primary"
                        >
                          <Star className="h-3 w-3" />
                        </Button>
                      )}
                      {onImageDelete && (
                        <Button
                          size="sm"
                          variant="danger"
                          onClick={() => handleDelete(image.id)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                  <div className="absolute bottom-2 left-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-black bg-opacity-50 text-white">
                      {image.image_type}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Image Preview Modal */}
      {selectedImage && (
        <Modal
          isOpen={!!selectedImage}
          onClose={() => setSelectedImage(null)}
          title={`${selectedImage.image_type} - ${selectedImage.is_primary ? 'Primary Image' : 'Image'}`}
          size="lg"
        >
          <div className="space-y-4">
            <img
              src={selectedImage.image_url}
              alt={selectedImage.alt_text || 'Diamond image'}
              className="w-full max-h-96 object-contain rounded-lg"
            />
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Type:</span> {selectedImage.image_type}
              </div>
              <div>
                <span className="font-medium">Primary:</span> {selectedImage.is_primary ? 'Yes' : 'No'}
              </div>
              {selectedImage.file_size && (
                <div>
                  <span className="font-medium">Size:</span> {(selectedImage.file_size / 1024).toFixed(1)} KB
                </div>
              )}
              {selectedImage.width && selectedImage.height && (
                <div>
                  <span className="font-medium">Dimensions:</span> {selectedImage.width} × {selectedImage.height}
                </div>
              )}
            </div>
          </div>
        </Modal>
      )}

      {/* Upload Modal */}
      {isUploadModalOpen && (
        <Modal
          isOpen={isUploadModalOpen}
          onClose={() => setIsUploadModalOpen(false)}
          title="Upload Diamond Image"
          size="md"
        >
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Select Image
              </label>
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Image Type
              </label>
              <select
                value={uploadType}
                onChange={(e) => setUploadType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {imageTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isPrimary"
                checked={uploadIsPrimary}
                onChange={(e) => setUploadIsPrimary(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="isPrimary" className="ml-2 text-sm text-gray-700">
                Set as primary image
              </label>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                variant="secondary"
                onClick={() => setIsUploadModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleUpload}
                disabled={!uploadFile || isUploading}
                isLoading={isUploading}
              >
                Upload
              </Button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default DiamondImageGallery;
