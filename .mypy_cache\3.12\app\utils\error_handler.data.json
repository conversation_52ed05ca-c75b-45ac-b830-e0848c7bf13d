{".class": "MypyFile", "_fullname": "app.utils.error_handler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.utils.error_handler.APIError", "name": "APIError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.APIError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.utils.error_handler", "mro": ["app.utils.error_handler.APIError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "message", "status_code", "payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.APIError.__init__", "name": "__init__", "type": null}}, "message": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "app.utils.error_handler.APIError.message", "name": "message", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "payload": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "app.utils.error_handler.APIError.payload", "name": "payload", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "status_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "app.utils.error_handler.APIError.status_code", "name": "status_code", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.APIError.to_dict", "name": "to_dict", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.utils.error_handler.APIError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.utils.error_handler.APIError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FreshTokenRequired": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.FreshTokenRequired", "kind": "Gdef"}, "HTTPException": {".class": "SymbolTableNode", "cross_ref": "werkzeug.exceptions.HTTPException", "kind": "Gdef"}, "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.exc.IntegrityError", "kind": "Gdef"}, "InvalidHeaderError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.InvalidHeaderError", "kind": "Gdef"}, "NoAuthorizationError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.NoAuthorizationError", "kind": "Gdef"}, "RevokedTokenError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.RevokedTokenError", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "marshmallow.exceptions.ValidationError", "kind": "Gdef"}, "WrongTokenError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.WrongTokenError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.error_handler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.error_handler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.error_handler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.error_handler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.error_handler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.error_handler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "handle_api_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.handle_api_error", "name": "handle_api_error", "type": null}}, "handle_errors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["func"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.handle_errors", "name": "handle_errors", "type": null}}, "handle_generic_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.handle_generic_error", "name": "handle_generic_error", "type": null}}, "handle_http_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.handle_http_error", "name": "handle_http_error", "type": null}}, "handle_integrity_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.handle_integrity_error", "name": "handle_integrity_error", "type": null}}, "handle_jwt_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.handle_jwt_error", "name": "handle_jwt_error", "type": null}}, "handle_validation_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.error_handler.handle_validation_error", "name": "handle_validation_error", "type": null}}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "jwt": {".class": "SymbolTableNode", "cross_ref": "jwt", "kind": "Gdef"}, "traceback": {".class": "SymbolTableNode", "cross_ref": "traceback", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\utils\\error_handler.py"}