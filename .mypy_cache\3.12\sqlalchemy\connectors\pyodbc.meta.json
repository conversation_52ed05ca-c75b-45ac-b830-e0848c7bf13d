{"data_mtime": 1753035428, "dep_lines": [27, 29, 19, 21, 23, 24, 25, 8, 10, 11, 12, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 10, 10, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.engine.interfaces", "sqlalchemy.sql.type_api", "urllib.parse", "sqlalchemy.connectors", "sqlalchemy.pool", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "re", "types", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.url", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.pool.base", "sqlalchemy.sql", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections"], "hash": "b217d36d49624c620c8c3bd061317ed2251c8cbb", "id": "sqlalchemy.connectors.pyodbc", "ignore_all": true, "interface_hash": "07b6d997503ea09de8207f51409f9cb95602b67e", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\connectors\\pyodbc.py", "plugin_data": null, "size": 8714, "suppressed": [], "version_id": "1.15.0"}