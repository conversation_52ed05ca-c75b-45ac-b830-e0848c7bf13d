{"data_mtime": 1753088336, "dep_lines": [3, 4, 6, 9, 10, 2, 5, 7, 8, 10, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 5, 10, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["app.models.diamond_image", "app.models.diamond", "app.utils.decorators", "werkzeug.utils", "PIL.Image", "flask", "app", "os", "uuid", "PIL", "logging", "builtins", "_collections_abc", "_frozen_importlib", "abc", "app.models", "app.utils", "flask.globals", "flask.wrappers", "flask_sqlalchemy", "flask_sqlalchemy.extension", "types", "typing", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.wrappers", "werkzeug.wrappers.request"], "hash": "fc63e47d0cf50e6f2879700dc891a29f30838319", "id": "app.api.diamond_images", "ignore_all": true, "interface_hash": "7144e5f9b06fba609c1e32c9ddead1a1f84735ad", "mtime": 1753087994, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\diamond_images.py", "plugin_data": null, "size": 9089, "suppressed": ["flask_restx"], "version_id": "1.15.0"}