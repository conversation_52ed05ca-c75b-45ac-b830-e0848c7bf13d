{"data_mtime": 1753035427, "dep_lines": [19, 20, 24, 25, 27, 28, 29, 17, 18, 7, 9, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql._typing", "sqlalchemy.sql.base", "sqlalchemy.sql.dml", "sqlalchemy.sql.elements", "sqlalchemy.sql.expression", "sqlalchemy.sql.selectable", "sqlalchemy.util.typing", "sqlalchemy.exc", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "_typeshed", "abc", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "types"], "hash": "ec52533f792afbca25fab134bcb0e6861f1d847e", "id": "sqlalchemy.dialects.mysql.dml", "ignore_all": true, "interface_hash": "8895f1475ecd2a0809e9c27ade30d9bdf958c70b", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\dml.py", "plugin_data": null, "size": 7993, "suppressed": [], "version_id": "1.15.0"}