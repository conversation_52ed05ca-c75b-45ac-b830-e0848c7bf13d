{"data_mtime": 1753035443, "dep_lines": [20, 21, 6, 7, 20, 1, 3, 4, 5, 8, 9, 10, 11, 12, 13, 1, 1, 1, 18, 17], "dep_prios": [10, 5, 10, 10, 20, 5, 10, 5, 10, 10, 10, 10, 10, 5, 5, 5, 30, 30, 5, 5], "dependencies": ["alembic.util.compat", "alembic.util.exc", "importlib.machinery", "importlib.util", "alembic.util", "__future__", "atexit", "contextlib", "importlib", "os", "pathlib", "re", "tempfile", "types", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "e410a9f5a1d420e7572568f3989a27bf4bf54996", "id": "alembic.util.pyfiles", "ignore_all": true, "interface_hash": "cf0a7eeb0cbbf9fa18d4bc46c18c17cb2d0f2f4f", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\util\\pyfiles.py", "plugin_data": null, "size": 4730, "suppressed": ["mako.template", "mako"], "version_id": "1.15.0"}