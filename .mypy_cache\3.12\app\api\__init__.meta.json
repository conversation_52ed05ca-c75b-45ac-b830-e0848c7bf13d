{"data_mtime": 1753088016, "dep_lines": [26, 27, 28, 29, 30, 31, 32, 33, 34, 2, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["app.api.auth", "app.api.users", "app.api.diamond", "app.api.vendor", "app.api.manufacturing", "app.api.jewelry", "app.api.sale", "app.api.dashboard", "app.api.upload", "flask", "builtins", "_frozen_importlib", "abc", "flask.blueprints", "flask.scaffold", "os", "typing"], "hash": "1e222674819aedcdcae50d75ed8b29c75d869bb8", "id": "app.api", "ignore_all": true, "interface_hash": "d433ae64ad9e5e294ac840e89e36b18afec4360d", "mtime": 1750959265, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\__init__.py", "plugin_data": null, "size": 1209, "suppressed": ["flask_restx"], "version_id": "1.15.0"}