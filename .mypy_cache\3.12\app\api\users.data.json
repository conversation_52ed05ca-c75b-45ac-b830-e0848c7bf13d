{".class": "MypyFile", "_fullname": "app.api.users", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIError": {".class": "SymbolTableNode", "cross_ref": "app.utils.error_handler.APIError", "kind": "Gdef"}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.users.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.users.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Resource", "source_any": null, "type_of_any": 3}}}, "User": {".class": "SymbolTableNode", "cross_ref": "app.models.user.User", "kind": "Gdef"}, "UserList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.users.UserList", "name": "UserList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.users.UserList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.users", "mro": ["app.api.users.UserList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.users.UserList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.users.UserList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.users.UserList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.users.UserList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserResource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.users.UserResource", "name": "UserResource", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.users.UserResource", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.users", "mro": ["app.api.users.UserResource", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.users.UserResource.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.users.UserResource.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.users.UserResource.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.users.UserResource.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.users.UserResource.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.users.UserResource.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.users.UserResource.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.users.UserResource", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserSchema": {".class": "SymbolTableNode", "cross_ref": "app.models.user.UserSchema", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "marshmallow.exceptions.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.users.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.users.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.users.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.users.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.users.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.users.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "admin_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.admin_required", "kind": "Gdef"}, "argon2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.users.argon2", "name": "argon2", "type": {".class": "AnyType", "missing_import_name": "app.api.users.argon2", "source_any": null, "type_of_any": 3}}}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.users.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.users.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.users.fields", "source_any": null, "type_of_any": 3}}}, "jwt_required": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.view_decorators.jwt_required", "kind": "Gdef"}, "user_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.users.user_model", "name": "user_model", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "users_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.users.users_ns", "name": "users_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.users.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\users.py"}