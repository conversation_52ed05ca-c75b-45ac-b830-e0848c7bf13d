{"data_mtime": 1753035427, "dep_lines": [17, 18, 19, 22, 23, 17, 7, 9, 10, 15, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 25, 25, 20, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.engine.interfaces", "sqlalchemy.sql.operators", "sqlalchemy.sql", "__future__", "datetime", "typing", "uuid", "builtins", "_frozen_importlib", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.base", "sqlalchemy.sql.visitors", "types"], "hash": "bb883040a2ec46a235bfac035e84590a3c8f1e24", "id": "sqlalchemy.dialects.postgresql.types", "ignore_all": true, "interface_hash": "07d0554a50c612b5c096b060fe09872c0d516bca", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\types.py", "plugin_data": null, "size": 7942, "suppressed": [], "version_id": "1.15.0"}