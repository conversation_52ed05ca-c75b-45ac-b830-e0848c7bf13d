{".class": "MypyFile", "_fullname": "_sqlite3", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Blob": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Blob", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Connection", "kind": "Gdef"}, "Cursor": {".class": "SymbolTableNode", "cross_ref": "sqlite3.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "DataError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DataError", "kind": "Gdef"}, "DatabaseError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.DatabaseError", "kind": "Gdef"}, "Error": {".class": "SymbolTableNode", "cross_ref": "sqlite3.E<PERSON>r", "kind": "Gdef"}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "IntegrityError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.IntegrityError", "kind": "Gdef"}, "InterfaceError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InterfaceError", "kind": "Gdef"}, "InternalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.InternalError", "kind": "Gdef"}, "LEGACY_TRANSACTION_CONTROL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.L<PERSON><PERSON>Y_TRANSACTION_CONTROL", "name": "LEGACY_TRANSACTION_CONTROL", "type": "builtins.int"}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NotSupportedError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.NotSupportedError", "kind": "Gdef"}, "OperationalError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.OperationalError", "kind": "Gdef"}, "PARSE_COLNAMES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.PARSE_COLNAMES", "name": "PARSE_COLNAMES", "type": "builtins.int"}}, "PARSE_DECLTYPES": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.PARSE_DECLTYPES", "name": "PARSE_DECLTYPES", "type": "builtins.int"}}, "PrepareProtocol": {".class": "SymbolTableNode", "cross_ref": "sqlite3.PrepareProtocol", "kind": "Gdef"}, "ProgrammingError": {".class": "SymbolTableNode", "cross_ref": "sqlite3.ProgrammingError", "kind": "Gdef"}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Row": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Row", "kind": "Gdef"}, "SQLITE_ABORT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ABORT", "name": "SQLITE_ABORT", "type": "builtins.int"}}, "SQLITE_ABORT_ROLLBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ABORT_ROLLBACK", "name": "SQLITE_ABORT_ROLLBACK", "type": "builtins.int"}}, "SQLITE_ALTER_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ALTER_TABLE", "name": "SQLITE_ALTER_TABLE", "type": "builtins.int"}}, "SQLITE_ANALYZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ANALYZE", "name": "SQLITE_ANALYZE", "type": "builtins.int"}}, "SQLITE_ATTACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ATTACH", "name": "SQLITE_ATTACH", "type": "builtins.int"}}, "SQLITE_AUTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_AUTH", "name": "SQLITE_AUTH", "type": "builtins.int"}}, "SQLITE_AUTH_USER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_AUTH_USER", "name": "SQLITE_AUTH_USER", "type": "builtins.int"}}, "SQLITE_BUSY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_BUSY", "name": "SQLITE_BUSY", "type": "builtins.int"}}, "SQLITE_BUSY_RECOVERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_BUSY_RECOVERY", "name": "SQLITE_BUSY_RECOVERY", "type": "builtins.int"}}, "SQLITE_BUSY_SNAPSHOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_BUSY_SNAPSHOT", "name": "SQLITE_BUSY_SNAPSHOT", "type": "builtins.int"}}, "SQLITE_BUSY_TIMEOUT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_BUSY_TIMEOUT", "name": "SQLITE_BUSY_TIMEOUT", "type": "builtins.int"}}, "SQLITE_CANTOPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CANTOPEN", "name": "SQLITE_CANTOPEN", "type": "builtins.int"}}, "SQLITE_CANTOPEN_CONVPATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CANTOPEN_CONVPATH", "name": "SQLITE_CANTOPEN_CONVPATH", "type": "builtins.int"}}, "SQLITE_CANTOPEN_DIRTYWAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CANTOPEN_DIRTYWAL", "name": "SQLITE_CANTOPEN_DIRTYWAL", "type": "builtins.int"}}, "SQLITE_CANTOPEN_FULLPATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CANTOPEN_FULLPATH", "name": "SQLITE_CANTOPEN_FULLPATH", "type": "builtins.int"}}, "SQLITE_CANTOPEN_ISDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CANTOPEN_ISDIR", "name": "SQLITE_CANTOPEN_ISDIR", "type": "builtins.int"}}, "SQLITE_CANTOPEN_NOTEMPDIR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CANTOPEN_NOTEMPDIR", "name": "SQLITE_CANTOPEN_NOTEMPDIR", "type": "builtins.int"}}, "SQLITE_CANTOPEN_SYMLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CANTOPEN_SYMLINK", "name": "SQLITE_CANTOPEN_SYMLINK", "type": "builtins.int"}}, "SQLITE_CONSTRAINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT", "name": "SQLITE_CONSTRAINT", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_CHECK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_CHECK", "name": "SQLITE_CONSTRAINT_CHECK", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_COMMITHOOK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_COMMITHOOK", "name": "SQLITE_CONSTRAINT_COMMITHOOK", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_FOREIGNKEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_FOREIGNKEY", "name": "SQLITE_CONSTRAINT_FOREIGNKEY", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_FUNCTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_FUNCTION", "name": "SQLITE_CONSTRAINT_FUNCTION", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_NOTNULL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_NOTNULL", "name": "SQLITE_CONSTRAINT_NOTNULL", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_PINNED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_PINNED", "name": "SQLITE_CONSTRAINT_PINNED", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_PRIMARYKEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_PRIMARYKEY", "name": "SQLITE_CONSTRAINT_PRIMARYKEY", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_ROWID": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_ROWID", "name": "SQLITE_CONSTRAINT_ROWID", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_TRIGGER", "name": "SQLITE_CONSTRAINT_TRIGGER", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_UNIQUE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_UNIQUE", "name": "SQLITE_CONSTRAINT_UNIQUE", "type": "builtins.int"}}, "SQLITE_CONSTRAINT_VTAB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CONSTRAINT_VTAB", "name": "SQLITE_CONSTRAINT_VTAB", "type": "builtins.int"}}, "SQLITE_CORRUPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CORRUPT", "name": "SQLITE_CORRUPT", "type": "builtins.int"}}, "SQLITE_CORRUPT_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CORRUPT_INDEX", "name": "SQLITE_CORRUPT_INDEX", "type": "builtins.int"}}, "SQLITE_CORRUPT_SEQUENCE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CORRUPT_SEQUENCE", "name": "SQLITE_CORRUPT_SEQUENCE", "type": "builtins.int"}}, "SQLITE_CORRUPT_VTAB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CORRUPT_VTAB", "name": "SQLITE_CORRUPT_VTAB", "type": "builtins.int"}}, "SQLITE_CREATE_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_INDEX", "name": "SQLITE_CREATE_INDEX", "type": "builtins.int"}}, "SQLITE_CREATE_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TABLE", "name": "SQLITE_CREATE_TABLE", "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_INDEX", "name": "SQLITE_CREATE_TEMP_INDEX", "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_TABLE", "name": "SQLITE_CREATE_TEMP_TABLE", "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_TRIGGER", "name": "SQLITE_CREATE_TEMP_TRIGGER", "type": "builtins.int"}}, "SQLITE_CREATE_TEMP_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TEMP_VIEW", "name": "SQLITE_CREATE_TEMP_VIEW", "type": "builtins.int"}}, "SQLITE_CREATE_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_TRIGGER", "name": "SQLITE_CREATE_TRIGGER", "type": "builtins.int"}}, "SQLITE_CREATE_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_VIEW", "name": "SQLITE_CREATE_VIEW", "type": "builtins.int"}}, "SQLITE_CREATE_VTABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_CREATE_VTABLE", "name": "SQLITE_CREATE_VTABLE", "type": "builtins.int"}}, "SQLITE_DBCONFIG_DEFENSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_DEFENSIVE", "name": "SQLITE_DBCONFIG_DEFENSIVE", "type": "builtins.int"}}, "SQLITE_DBCONFIG_DQS_DDL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_DQS_DDL", "name": "SQLITE_DBCONFIG_DQS_DDL", "type": "builtins.int"}}, "SQLITE_DBCONFIG_DQS_DML": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_DQS_DML", "name": "SQLITE_DBCONFIG_DQS_DML", "type": "builtins.int"}}, "SQLITE_DBCONFIG_ENABLE_FKEY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_ENABLE_FKEY", "name": "SQLITE_DBCONFIG_ENABLE_FKEY", "type": "builtins.int"}}, "SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER", "name": "SQLITE_DBCONFIG_ENABLE_FTS3_TOKENIZER", "type": "builtins.int"}}, "SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION", "name": "SQLITE_DBCONFIG_ENABLE_LOAD_EXTENSION", "type": "builtins.int"}}, "SQLITE_DBCONFIG_ENABLE_QPSG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_ENABLE_QPSG", "name": "SQLITE_DBCONFIG_ENABLE_QPSG", "type": "builtins.int"}}, "SQLITE_DBCONFIG_ENABLE_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_ENABLE_TRIGGER", "name": "SQLITE_DBCONFIG_ENABLE_TRIGGER", "type": "builtins.int"}}, "SQLITE_DBCONFIG_ENABLE_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_ENABLE_VIEW", "name": "SQLITE_DBCONFIG_ENABLE_VIEW", "type": "builtins.int"}}, "SQLITE_DBCONFIG_LEGACY_ALTER_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_LEGACY_ALTER_TABLE", "name": "SQLITE_DBCONFIG_LEGACY_ALTER_TABLE", "type": "builtins.int"}}, "SQLITE_DBCONFIG_LEGACY_FILE_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_LEGACY_FILE_FORMAT", "name": "SQLITE_DBCONFIG_LEGACY_FILE_FORMAT", "type": "builtins.int"}}, "SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE", "name": "SQLITE_DBCONFIG_NO_CKPT_ON_CLOSE", "type": "builtins.int"}}, "SQLITE_DBCONFIG_RESET_DATABASE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_RESET_DATABASE", "name": "SQLITE_DBCONFIG_RESET_DATABASE", "type": "builtins.int"}}, "SQLITE_DBCONFIG_TRIGGER_EQP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_TRIGGER_EQP", "name": "SQLITE_DBCONFIG_TRIGGER_EQP", "type": "builtins.int"}}, "SQLITE_DBCONFIG_TRUSTED_SCHEMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_TRUSTED_SCHEMA", "name": "SQLITE_DBCONFIG_TRUSTED_SCHEMA", "type": "builtins.int"}}, "SQLITE_DBCONFIG_WRITABLE_SCHEMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DBCONFIG_WRITABLE_SCHEMA", "name": "SQLITE_DBCONFIG_WRITABLE_SCHEMA", "type": "builtins.int"}}, "SQLITE_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DELETE", "name": "SQLITE_DELETE", "type": "builtins.int"}}, "SQLITE_DENY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DENY", "name": "SQLITE_DENY", "type": "builtins.int"}}, "SQLITE_DETACH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DETACH", "name": "SQLITE_DETACH", "type": "builtins.int"}}, "SQLITE_DONE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DONE", "name": "SQLITE_DONE", "type": "builtins.int"}}, "SQLITE_DROP_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_INDEX", "name": "SQLITE_DROP_INDEX", "type": "builtins.int"}}, "SQLITE_DROP_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TABLE", "name": "SQLITE_DROP_TABLE", "type": "builtins.int"}}, "SQLITE_DROP_TEMP_INDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_INDEX", "name": "SQLITE_DROP_TEMP_INDEX", "type": "builtins.int"}}, "SQLITE_DROP_TEMP_TABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_TABLE", "name": "SQLITE_DROP_TEMP_TABLE", "type": "builtins.int"}}, "SQLITE_DROP_TEMP_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_TRIGGER", "name": "SQLITE_DROP_TEMP_TRIGGER", "type": "builtins.int"}}, "SQLITE_DROP_TEMP_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TEMP_VIEW", "name": "SQLITE_DROP_TEMP_VIEW", "type": "builtins.int"}}, "SQLITE_DROP_TRIGGER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_TRIGGER", "name": "SQLITE_DROP_TRIGGER", "type": "builtins.int"}}, "SQLITE_DROP_VIEW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_VIEW", "name": "SQLITE_DROP_VIEW", "type": "builtins.int"}}, "SQLITE_DROP_VTABLE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_DROP_VTABLE", "name": "SQLITE_DROP_VTABLE", "type": "builtins.int"}}, "SQLITE_EMPTY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_EMPTY", "name": "SQLITE_EMPTY", "type": "builtins.int"}}, "SQLITE_ERROR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ERROR", "name": "SQLITE_ERROR", "type": "builtins.int"}}, "SQLITE_ERROR_MISSING_COLLSEQ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ERROR_MISSING_COLLSEQ", "name": "SQLITE_ERROR_MISSING_COLLSEQ", "type": "builtins.int"}}, "SQLITE_ERROR_RETRY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ERROR_RETRY", "name": "SQLITE_ERROR_RETRY", "type": "builtins.int"}}, "SQLITE_ERROR_SNAPSHOT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ERROR_SNAPSHOT", "name": "SQLITE_ERROR_SNAPSHOT", "type": "builtins.int"}}, "SQLITE_FORMAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_FORMAT", "name": "SQLITE_FORMAT", "type": "builtins.int"}}, "SQLITE_FULL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_FULL", "name": "SQLITE_FULL", "type": "builtins.int"}}, "SQLITE_FUNCTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_FUNCTION", "name": "SQLITE_FUNCTION", "type": "builtins.int"}}, "SQLITE_IGNORE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IGNORE", "name": "SQLITE_IGNORE", "type": "builtins.int"}}, "SQLITE_INSERT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_INSERT", "name": "SQLITE_INSERT", "type": "builtins.int"}}, "SQLITE_INTERNAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_INTERNAL", "name": "SQLITE_INTERNAL", "type": "builtins.int"}}, "SQLITE_INTERRUPT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_INTERRUPT", "name": "SQLITE_INTERRUPT", "type": "builtins.int"}}, "SQLITE_IOERR": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR", "name": "SQLITE_IOERR", "type": "builtins.int"}}, "SQLITE_IOERR_ACCESS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_ACCESS", "name": "SQLITE_IOERR_ACCESS", "type": "builtins.int"}}, "SQLITE_IOERR_AUTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_AUTH", "name": "SQLITE_IOERR_AUTH", "type": "builtins.int"}}, "SQLITE_IOERR_BEGIN_ATOMIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_BEGIN_ATOMIC", "name": "SQLITE_IOERR_BEGIN_ATOMIC", "type": "builtins.int"}}, "SQLITE_IOERR_BLOCKED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_BLOCKED", "name": "SQLITE_IOERR_BLOCKED", "type": "builtins.int"}}, "SQLITE_IOERR_CHECKRESERVEDLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_CHECKRESERVEDLOCK", "name": "SQLITE_IOERR_CHECKRESERVEDLOCK", "type": "builtins.int"}}, "SQLITE_IOERR_CLOSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_CLOSE", "name": "SQLITE_IOERR_CLOSE", "type": "builtins.int"}}, "SQLITE_IOERR_COMMIT_ATOMIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_COMMIT_ATOMIC", "name": "SQLITE_IOERR_COMMIT_ATOMIC", "type": "builtins.int"}}, "SQLITE_IOERR_CONVPATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_CONVPATH", "name": "SQLITE_IOERR_CONVPATH", "type": "builtins.int"}}, "SQLITE_IOERR_CORRUPTFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_CORRUPTFS", "name": "SQLITE_IOERR_CORRUPTFS", "type": "builtins.int"}}, "SQLITE_IOERR_DATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_DATA", "name": "SQLITE_IOERR_DATA", "type": "builtins.int"}}, "SQLITE_IOERR_DELETE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_DELETE", "name": "SQLITE_IOERR_DELETE", "type": "builtins.int"}}, "SQLITE_IOERR_DELETE_NOENT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_DELETE_NOENT", "name": "SQLITE_IOERR_DELETE_NOENT", "type": "builtins.int"}}, "SQLITE_IOERR_DIR_CLOSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_DIR_CLOSE", "name": "SQLITE_IOERR_DIR_CLOSE", "type": "builtins.int"}}, "SQLITE_IOERR_DIR_FSYNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_DIR_FSYNC", "name": "SQLITE_IOERR_DIR_FSYNC", "type": "builtins.int"}}, "SQLITE_IOERR_FSTAT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_FSTAT", "name": "SQLITE_IOERR_FSTAT", "type": "builtins.int"}}, "SQLITE_IOERR_FSYNC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_FSYNC", "name": "SQLITE_IOERR_FSYNC", "type": "builtins.int"}}, "SQLITE_IOERR_GETTEMPPATH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_GETTEMPPATH", "name": "SQLITE_IOERR_GETTEMPPATH", "type": "builtins.int"}}, "SQLITE_IOERR_LOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_LOCK", "name": "SQLITE_IOERR_LOCK", "type": "builtins.int"}}, "SQLITE_IOERR_MMAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_MMAP", "name": "SQLITE_IOERR_MMAP", "type": "builtins.int"}}, "SQLITE_IOERR_NOMEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_NOMEM", "name": "SQLITE_IOERR_NOMEM", "type": "builtins.int"}}, "SQLITE_IOERR_RDLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_RDLOCK", "name": "SQLITE_IOERR_RDLOCK", "type": "builtins.int"}}, "SQLITE_IOERR_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_READ", "name": "SQLITE_IOERR_READ", "type": "builtins.int"}}, "SQLITE_IOERR_ROLLBACK_ATOMIC": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_ROLLBACK_ATOMIC", "name": "SQLITE_IOERR_ROLLBACK_ATOMIC", "type": "builtins.int"}}, "SQLITE_IOERR_SEEK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_SEEK", "name": "SQLITE_IOERR_SEEK", "type": "builtins.int"}}, "SQLITE_IOERR_SHMLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_SHMLOCK", "name": "SQLITE_IOERR_SHMLOCK", "type": "builtins.int"}}, "SQLITE_IOERR_SHMMAP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_SHMMAP", "name": "SQLITE_IOERR_SHMMAP", "type": "builtins.int"}}, "SQLITE_IOERR_SHMOPEN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_SHMOPEN", "name": "SQLITE_IOERR_SHMOPEN", "type": "builtins.int"}}, "SQLITE_IOERR_SHMSIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_SHMSIZE", "name": "SQLITE_IOERR_SHMSIZE", "type": "builtins.int"}}, "SQLITE_IOERR_SHORT_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_SHORT_READ", "name": "SQLITE_IOERR_SHORT_READ", "type": "builtins.int"}}, "SQLITE_IOERR_TRUNCATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_TRUNCATE", "name": "SQLITE_IOERR_TRUNCATE", "type": "builtins.int"}}, "SQLITE_IOERR_UNLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_UNLOCK", "name": "SQLITE_IOERR_UNLOCK", "type": "builtins.int"}}, "SQLITE_IOERR_VNODE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_VNODE", "name": "SQLITE_IOERR_VNODE", "type": "builtins.int"}}, "SQLITE_IOERR_WRITE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_IOERR_WRITE", "name": "SQLITE_IOERR_WRITE", "type": "builtins.int"}}, "SQLITE_LIMIT_ATTACHED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_ATTACHED", "name": "SQLITE_LIMIT_ATTACHED", "type": "builtins.int"}}, "SQLITE_LIMIT_COLUMN": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_COLUMN", "name": "SQLITE_LIMIT_COLUMN", "type": "builtins.int"}}, "SQLITE_LIMIT_COMPOUND_SELECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_COMPOUND_SELECT", "name": "SQLITE_LIMIT_COMPOUND_SELECT", "type": "builtins.int"}}, "SQLITE_LIMIT_EXPR_DEPTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_EXPR_DEPTH", "name": "SQLITE_LIMIT_EXPR_DEPTH", "type": "builtins.int"}}, "SQLITE_LIMIT_FUNCTION_ARG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_FUNCTION_ARG", "name": "SQLITE_LIMIT_FUNCTION_ARG", "type": "builtins.int"}}, "SQLITE_LIMIT_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_LENGTH", "name": "SQLITE_LIMIT_LENGTH", "type": "builtins.int"}}, "SQLITE_LIMIT_LIKE_PATTERN_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_LIKE_PATTERN_LENGTH", "name": "SQLITE_LIMIT_LIKE_PATTERN_LENGTH", "type": "builtins.int"}}, "SQLITE_LIMIT_SQL_LENGTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_SQL_LENGTH", "name": "SQLITE_LIMIT_SQL_LENGTH", "type": "builtins.int"}}, "SQLITE_LIMIT_TRIGGER_DEPTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_TRIGGER_DEPTH", "name": "SQLITE_LIMIT_TRIGGER_DEPTH", "type": "builtins.int"}}, "SQLITE_LIMIT_VARIABLE_NUMBER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_VARIABLE_NUMBER", "name": "SQLITE_LIMIT_VARIABLE_NUMBER", "type": "builtins.int"}}, "SQLITE_LIMIT_VDBE_OP": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_VDBE_OP", "name": "SQLITE_LIMIT_VDBE_OP", "type": "builtins.int"}}, "SQLITE_LIMIT_WORKER_THREADS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LIMIT_WORKER_THREADS", "name": "SQLITE_LIMIT_WORKER_THREADS", "type": "builtins.int"}}, "SQLITE_LOCKED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LOCKED", "name": "SQLITE_LOCKED", "type": "builtins.int"}}, "SQLITE_LOCKED_SHAREDCACHE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LOCKED_SHAREDCACHE", "name": "SQLITE_LOCKED_SHAREDCACHE", "type": "builtins.int"}}, "SQLITE_LOCKED_VTAB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_LOCKED_VTAB", "name": "SQLITE_LOCKED_VTAB", "type": "builtins.int"}}, "SQLITE_MISMATCH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_MISMATCH", "name": "SQLITE_MISMATCH", "type": "builtins.int"}}, "SQLITE_MISUSE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_MISUSE", "name": "SQLITE_MISUSE", "type": "builtins.int"}}, "SQLITE_NOLFS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_NOLFS", "name": "SQLITE_NOLFS", "type": "builtins.int"}}, "SQLITE_NOMEM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_NOMEM", "name": "SQLITE_NOMEM", "type": "builtins.int"}}, "SQLITE_NOTADB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_NOTADB", "name": "SQLITE_NOTADB", "type": "builtins.int"}}, "SQLITE_NOTFOUND": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_NOTFOUND", "name": "SQLITE_NOTFOUND", "type": "builtins.int"}}, "SQLITE_NOTICE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_NOTICE", "name": "SQLITE_NOTICE", "type": "builtins.int"}}, "SQLITE_NOTICE_RECOVER_ROLLBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_NOTICE_RECOVER_ROLLBACK", "name": "SQLITE_NOTICE_RECOVER_ROLLBACK", "type": "builtins.int"}}, "SQLITE_NOTICE_RECOVER_WAL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_NOTICE_RECOVER_WAL", "name": "SQLITE_NOTICE_RECOVER_WAL", "type": "builtins.int"}}, "SQLITE_OK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_OK", "name": "SQLITE_OK", "type": "builtins.int"}}, "SQLITE_OK_LOAD_PERMANENTLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_OK_LOAD_PERMANENTLY", "name": "SQLITE_OK_LOAD_PERMANENTLY", "type": "builtins.int"}}, "SQLITE_OK_SYMLINK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_OK_SYMLINK", "name": "SQLITE_OK_SYMLINK", "type": "builtins.int"}}, "SQLITE_PERM": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_PERM", "name": "SQLITE_PERM", "type": "builtins.int"}}, "SQLITE_PRAGMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_PRAGMA", "name": "SQLITE_PRAGMA", "type": "builtins.int"}}, "SQLITE_PROTOCOL": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_PROTOCOL", "name": "SQLITE_PROTOCOL", "type": "builtins.int"}}, "SQLITE_RANGE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_RANGE", "name": "SQLITE_RANGE", "type": "builtins.int"}}, "SQLITE_READ": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READ", "name": "SQLITE_READ", "type": "builtins.int"}}, "SQLITE_READONLY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READONLY", "name": "SQLITE_READONLY", "type": "builtins.int"}}, "SQLITE_READONLY_CANTINIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READONLY_CANTINIT", "name": "SQLITE_READONLY_CANTINIT", "type": "builtins.int"}}, "SQLITE_READONLY_CANTLOCK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READONLY_CANTLOCK", "name": "SQLITE_READONLY_CANTLOCK", "type": "builtins.int"}}, "SQLITE_READONLY_DBMOVED": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READONLY_DBMOVED", "name": "SQLITE_READONLY_DBMOVED", "type": "builtins.int"}}, "SQLITE_READONLY_DIRECTORY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READONLY_DIRECTORY", "name": "SQLITE_READONLY_DIRECTORY", "type": "builtins.int"}}, "SQLITE_READONLY_RECOVERY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READONLY_RECOVERY", "name": "SQLITE_READONLY_RECOVERY", "type": "builtins.int"}}, "SQLITE_READONLY_ROLLBACK": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_READONLY_ROLLBACK", "name": "SQLITE_READONLY_ROLLBACK", "type": "builtins.int"}}, "SQLITE_RECURSIVE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_RECURSIVE", "name": "SQLITE_RECURSIVE", "type": "builtins.int"}}, "SQLITE_REINDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_REINDEX", "name": "SQLITE_REINDEX", "type": "builtins.int"}}, "SQLITE_ROW": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_ROW", "name": "SQLITE_ROW", "type": "builtins.int"}}, "SQLITE_SAVEPOINT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_SAVEPOINT", "name": "SQLITE_SAVEPOINT", "type": "builtins.int"}}, "SQLITE_SCHEMA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_SCHEMA", "name": "SQLITE_SCHEMA", "type": "builtins.int"}}, "SQLITE_SELECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_SELECT", "name": "SQLITE_SELECT", "type": "builtins.int"}}, "SQLITE_TOOBIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_TOOBIG", "name": "SQLITE_TOOBIG", "type": "builtins.int"}}, "SQLITE_TRANSACTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_TRANSACTION", "name": "SQLITE_TRANSACTION", "type": "builtins.int"}}, "SQLITE_UPDATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_UPDATE", "name": "SQLITE_UPDATE", "type": "builtins.int"}}, "SQLITE_WARNING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_WARNING", "name": "SQLITE_WARNING", "type": "builtins.int"}}, "SQLITE_WARNING_AUTOINDEX": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.SQLITE_WARNING_AUTOINDEX", "name": "SQLITE_WARNING_AUTOINDEX", "type": "builtins.int"}}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Warning": {".class": "SymbolTableNode", "cross_ref": "sqlite3.Warning", "kind": "Gdef"}, "_Adapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": 1, "name": "_T", "namespace": "_sqlite3._Adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "_sqlite3._Adapter", "line": 29, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": 1, "name": "_T", "namespace": "_sqlite3._Adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "_sqlite3._SqliteData"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ConnectionT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "name": "_ConnectionT", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "_Converter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_sqlite3._Converter", "line": 30, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bytes"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_SqliteData": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "_sqlite3._SqliteData", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "_collections_abc.<PERSON><PERSON>er", "builtins.int", "builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "adapt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_sqlite3.adapt", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.adapt", "name": "adapt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.adapt", "name": "adapt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.adapt", "name": "adapt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.adapt", "name": "adapt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adapt", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.adapt", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "adapters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.adapters", "name": "adapters", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "type_ref": "_sqlite3._Adapter"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "complete_statement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["statement"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.complete_statement", "name": "complete_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["statement"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "complete_statement", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "connect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_sqlite3.connect", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri", "autocommit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": "sqlite3.Connection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": "sqlite3.Connection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_sqlite3.connect", "name": "connect", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": "sqlite3.Connection", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1, 1, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect#1", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 3, 5, 5, 5], "arg_names": ["database", "timeout", "detect_types", "isolation_level", "check_same_thread", "factory", "cached_statements", "uri", "autocommit"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, "builtins.float", "builtins.int", {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "DEFERRED"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "EXCLUSIVE"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "IMMEDIATE"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}}, "builtins.int", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "connect", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._ConnectionT", "id": -1, "name": "_ConnectionT", "namespace": "_sqlite3.connect", "upper_bound": "sqlite3.Connection", "values": [], "variance": 0}]}]}}}, "converters": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.converters", "name": "converters", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_sqlite3._Converter"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "enable_callback_tracebacks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.enable_callback_tracebacks", "name": "enable_callback_tracebacks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enable_callback_tracebacks", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "register_adapter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.register_adapter", "name": "register_adapter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.register_adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.register_adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "_sqlite3._Adapter"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_adapter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_sqlite3._T", "id": -1, "name": "_T", "namespace": "_sqlite3.register_adapter", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "register_converter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_sqlite3.register_converter", "name": "register_converter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_sqlite3._Converter"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "register_converter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqlite_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_sqlite3.sqlite_version", "name": "sqlite_version", "type": "builtins.str"}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}, "threadsafety": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_final", "is_ready"], "fullname": "_sqlite3.threadsafety", "name": "threadsafety", "type": "builtins.int"}}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\_sqlite3.pyi"}