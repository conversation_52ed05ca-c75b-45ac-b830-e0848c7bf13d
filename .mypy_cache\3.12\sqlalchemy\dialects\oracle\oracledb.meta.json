{"data_mtime": 1753035436, "dep_lines": [602, 602, 605, 609, 603, 604, 609, 610, 595, 597, 598, 599, 603, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 615], "dep_prios": [10, 20, 5, 10, 10, 10, 20, 5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.oracle.cx_oracle", "sqlalchemy.dialects.oracle", "sqlalchemy.connectors.asyncio", "sqlalchemy.engine.default", "sqlalchemy.exc", "sqlalchemy.pool", "sqlalchemy.engine", "sqlalchemy.util", "__future__", "collections", "re", "typing", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.connectors", "sqlalchemy.dialects.oracle.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry"], "hash": "53a44b24dd59db933899fb27000608c8a43e3515", "id": "sqlalchemy.dialects.oracle.oracledb", "ignore_all": true, "interface_hash": "af6599230d4bee0f8575baad0db059601bfd0c88", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\oracledb.py", "plugin_data": null, "size": 34718, "suppressed": ["oracledb"], "version_id": "1.15.0"}