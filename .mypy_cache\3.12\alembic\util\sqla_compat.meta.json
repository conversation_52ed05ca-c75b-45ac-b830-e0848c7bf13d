{"data_mtime": 1753035428, "dep_lines": [28, 29, 30, 38, 49, 51, 22, 23, 24, 45, 134, 4, 6, 7, 8, 21, 35, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 25, 25, 5, 10, 10, 25, 5, 5, 10, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.sql.visitors", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "sqlalchemy.sql.naming", "sqlalchemy.sql.compiler", "sqlalchemy.sql.schema", "sqlalchemy.schema", "sqlalchemy.sql", "sqlalchemy.types", "sqlalchemy.engine", "sqlalchemy.util", "__future__", "contextlib", "re", "typing", "sqlalchemy", "typing_extensions", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.util", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.util.langhelpers", "types"], "hash": "f63217d293e278ee0217faa566aa9f7362d033a1", "id": "alembic.util.sqla_compat", "ignore_all": true, "interface_hash": "6af7c6535349ff152bd6a2a20b87d41f19740fcb", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\util\\sqla_compat.py", "plugin_data": null, "size": 14785, "suppressed": [], "version_id": "1.15.0"}