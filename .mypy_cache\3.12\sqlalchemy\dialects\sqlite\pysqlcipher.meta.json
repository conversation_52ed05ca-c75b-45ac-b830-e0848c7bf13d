{"data_mtime": 1753035439, "dep_lines": [101, 102, 102, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 114, 120], "dep_prios": [5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20, 20], "dependencies": ["sqlalchemy.dialects.sqlite.pysqlite", "sqlalchemy.pool", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.dialects.sqlite.base", "sqlalchemy.engine", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "typing"], "hash": "4832d8db30b94594d6f11466c30a4091b28d93a3", "id": "sqlalchemy.dialects.sqlite.pysqlcipher", "ignore_all": true, "interface_hash": "01cd39b1b712dddec45ce519b106c7266f44d59e", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\sqlite\\pysqlcipher.py", "plugin_data": null, "size": 5528, "suppressed": ["sqlcipher3", "pysqlcipher3"], "version_id": "1.15.0"}