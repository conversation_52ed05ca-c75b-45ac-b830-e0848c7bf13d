{"data_mtime": 1753035432, "dep_lines": [20, 25, 26, 27, 30, 31, 33, 37, 38, 43, 23, 24, 25, 9, 11, 12, 23, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 25, 25, 25, 25, 25, 25, 10, 10, 20, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.operators", "sqlalchemy.sql.expression", "sqlalchemy.sql.operators", "sqlalchemy.sql.visitors", "sqlalchemy.engine.interfaces", "sqlalchemy.sql._typing", "sqlalchemy.sql.elements", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.types", "sqlalchemy.util", "sqlalchemy.sql", "__future__", "re", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.lambdas", "sqlalchemy.sql.roles", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.util.langhelpers", "types"], "hash": "47e611c358bf06f1dff4c84cc0c7bb2f89079806", "id": "sqlalchemy.dialects.postgresql.array", "ignore_all": true, "interface_hash": "d00506a815aed4e6e68811ebff6477222e8b0d9b", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\array.py", "plugin_data": null, "size": 17495, "suppressed": [], "version_id": "1.15.0"}