{"data_mtime": 1753035424, "dep_lines": [7, 14, 22, 5, 8, 9, 10, 11, 12, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "tomli._re", "tomli._types", "__future__", "string", "sys", "types", "typing", "warnings", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc"], "hash": "72b8e73319af05717105a3d1754a53834bd986e7", "id": "tomli._parser", "ignore_all": true, "interface_hash": "2fdc534b6b3d21cd05c80105b39026d38cdaab06", "mtime": 1741848287, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\tomli\\_parser.py", "plugin_data": null, "size": 25591, "suppressed": [], "version_id": "1.15.0"}