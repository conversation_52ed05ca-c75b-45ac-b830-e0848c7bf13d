{"data_mtime": 1753035438, "dep_lines": [12, 13, 18, 19, 25, 22, 23, 24, 25, 8, 10, 22, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 20, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.pg_catalog", "sqlalchemy.engine.processors", "sqlalchemy.exc", "sqlalchemy.types", "sqlalchemy.util", "sqlalchemy.engine", "__future__", "decimal", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.engine.default", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql", "sqlalchemy.sql.base", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "typing"], "hash": "91b3c0295520dad134e73a90fc424fe9ceff4d4d", "id": "sqlalchemy.dialects.postgresql._psycopg_common", "ignore_all": true, "interface_hash": "7ce9c56171db22ecf570073f703c3c0a345aec12", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\_psycopg_common.py", "plugin_data": null, "size": 5860, "suppressed": [], "version_id": "1.15.0"}