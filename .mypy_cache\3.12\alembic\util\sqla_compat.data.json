{".class": "MypyFile", "_fullname": "alembic.util.sqla_compat", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AUTOINCREMENT_DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat.AUTOINCREMENT_DEFAULT", "name": "AUTOINCREMENT_DEFAULT", "type": "builtins.str"}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BindParameter": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.BindParameter", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CheckConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.CheckConstraint", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnClause", "kind": "Gdef"}, "ColumnCollection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.ColumnCollection", "kind": "Gdef"}, "ColumnElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ColumnElement", "kind": "Gdef"}, "Connection": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Connection", "kind": "Gdef"}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "Dialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.interfaces.Dialect", "kind": "Gdef"}, "DialectKWArgs": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.DialectKWArgs", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "Identity": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Identity", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Protocol": {".class": "SymbolTableNode", "cross_ref": "typing.Protocol", "kind": "Gdef"}, "SQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.compiler.SQLCompiler", "kind": "Gdef"}, "SchemaItem": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.SchemaItem", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Transaction": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.base.Transaction", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "UnaryExpression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.UnaryExpression", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_CE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CE", "name": "_CE", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.schema.SchemaItem"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "_CompilerProtocol": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["__call__", 2]], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.util.sqla_compat._CompilerProtocol", "name": "_CompilerProtocol", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract", "is_protocol"], "fullname": "alembic.util.sqla_compat._CompilerProtocol", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "alembic.util.sqla_compat", "mro": ["alembic.util.sqla_compat._CompilerProtocol", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 2, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_body"], "fullname": "alembic.util.sqla_compat._CompilerProtocol.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "element", "compiler", "kw"], "arg_types": ["alembic.util.sqla_compat._CompilerProtocol", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of _CompilerProtocol", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CompilerProtocol.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.util.sqla_compat._CompilerProtocol", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ConstraintName": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.util.sqla_compat._ConstraintName", "line": 137, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "NoneType"}, "builtins.str", "sqlalchemy.sql.base._NoneName"], "uses_pep604_syntax": false}}}, "_ConstraintNameDefined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.util.sqla_compat._ConstraintNameDefined", "line": 138, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.base._NoneName"], "uses_pep604_syntax": false}}}, "_NONE_NAME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NONE_NAME", "kind": "Gdef"}, "_NoneName": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base._NoneName", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.sqla_compat.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.sqla_compat.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.sqla_compat.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.sqla_compat.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.sqla_compat.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.util.sqla_compat.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.__version__", "kind": "Gdef"}, "_columns_for_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._columns_for_constraint", "name": "_columns_for_constraint", "type": null}}, "_connectable_has_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["connectable", "tablename", "schemaname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._connectable_has_table", "name": "_connectable_has_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["connectable", "tablename", "schemaname"], "arg_types": ["sqlalchemy.engine.base.Connection", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_connectable_has_table", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_constraint_is_named": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["constraint", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._constraint_is_named", "name": "_constraint_is_named", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["constraint", "dialect"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_constraint_is_named", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_copy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["schema_item", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._copy", "name": "_copy", "type": {".class": "CallableType", "arg_kinds": [0, 4], "arg_names": ["schema_item", "kw"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CE", "id": -1, "name": "_CE", "namespace": "alembic.util.sqla_compat._copy", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.schema.SchemaItem"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CE", "id": -1, "name": "_CE", "namespace": "alembic.util.sqla_compat._copy", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.schema.SchemaItem"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CE", "id": -1, "name": "_CE", "namespace": "alembic.util.sqla_compat._copy", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.schema.SchemaItem"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "_copy_expression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expression", "target_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._copy_expression", "name": "_copy_expression", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expression", "target_table"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CE", "id": -1, "name": "_CE", "namespace": "alembic.util.sqla_compat._copy_expression", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.schema.SchemaItem"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_copy_expression", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CE", "id": -1, "name": "_CE", "namespace": "alembic.util.sqla_compat._copy_expression", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.schema.SchemaItem"], "uses_pep604_syntax": false}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._CE", "id": -1, "name": "_CE", "namespace": "alembic.util.sqla_compat._copy_expression", "upper_bound": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, "sqlalchemy.sql.schema.SchemaItem"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "_ensure_scope_for_ddl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.util.sqla_compat._ensure_scope_for_ddl", "name": "_ensure_scope_for_ddl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["connection"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.engine.base.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_scope_for_ddl", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.util.sqla_compat._ensure_scope_for_ddl", "name": "_ensure_scope_for_ddl", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["connection"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.engine.base.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_scope_for_ddl", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_exec_on_inspector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["inspector", "statement", "params"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._exec_on_inspector", "name": "_exec_on_inspector", "type": null}}, "_find_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["clause"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._find_columns", "name": "_find_columns", "type": null}}, "_fk_is_self_referential": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._fk_is_self_referential", "name": "_fk_is_self_referential", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["constraint"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fk_is_self_referential", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fk_spec": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._fk_spec", "name": "_fk_spec", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["constraint"], "arg_types": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fk_spec", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_connection_in_transaction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._get_connection_in_transaction", "name": "_get_connection_in_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["connection"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.engine.base.Connection", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_connection_in_transaction", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_constraint_final_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["constraint", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._get_constraint_final_name", "name": "_get_constraint_final_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["constraint", "dialect"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.schema.Index", "sqlalchemy.sql.schema.Constraint"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.engine.interfaces.Dialect", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_constraint_final_name", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_identity_options_dict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["identity", "dialect_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._get_identity_options_dict", "name": "_get_identity_options_dict", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["identity", "dialect_kwargs"], "arg_types": [{".class": "UnionType", "items": ["sqlalchemy.sql.schema.Identity", "sqlalchemy.sql.schema.Sequence", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_identity_options_dict", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_variant_mapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._get_variant_mapping", "name": "_get_variant_mapping", "type": null}}, "_idx_table_bound_expressions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["idx"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._idx_table_bound_expressions", "name": "_idx_table_bound_expressions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["idx"], "arg_types": ["sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_idx_table_bound_expressions", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_type_bound": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._is_type_bound", "name": "_is_type_bound", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["constraint"], "arg_types": ["sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_type_bound", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_literal_bindparam": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.BindParameter"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.util.sqla_compat._literal_bindparam", "name": "_literal_bindparam", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._literal_bindparam", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.util.sqla_compat", "mro": ["alembic.util.sqla_compat._literal_bindparam", "sqlalchemy.sql.elements.BindParameter", "sqlalchemy.sql.roles.InElementRole", "sqlalchemy.sql.elements.KeyedColumnElement", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._literal_bindparam.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.util.sqla_compat._literal_bindparam", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_nullability_might_be_unset": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["metadata_column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._nullability_might_be_unset", "name": "_nullability_might_be_unset", "type": null}}, "_remove_column_from_collection": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["collection", "column"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._remove_column_from_collection", "name": "_remove_column_from_collection", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["collection", "column"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.base.ColumnCollection"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnClause"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_column_from_collection", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_literal_bindparam": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.util.sqla_compat._render_literal_bindparam", "name": "_render_literal_bindparam", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.util.sqla_compat._literal_bindparam", "sqlalchemy.sql.compiler.SQLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_literal_bindparam", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.util.sqla_compat._render_literal_bindparam", "name": "_render_literal_bindparam", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "_render_textual_index_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.util.sqla_compat._render_textual_index_column", "name": "_render_textual_index_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.util.sqla_compat._textual_index_element", "sqlalchemy.sql.compiler.SQLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_textual_index_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.util.sqla_compat._render_textual_index_column", "name": "_render_textual_index_column", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "_resolve_for_variant": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["type_", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._resolve_for_variant", "name": "_resolve_for_variant", "type": null}}, "_safe_begin_connection_transaction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._safe_begin_connection_transaction", "name": "_safe_begin_connection_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["connection"], "arg_types": ["sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_safe_begin_connection_transaction", "ret_type": "sqlalchemy.engine.base.Transaction", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_safe_commit_connection_transaction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._safe_commit_connection_transaction", "name": "_safe_commit_connection_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["connection"], "arg_types": ["sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_safe_commit_connection_transaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_safe_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._safe_int", "name": "_safe_int", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_safe_int", "ret_type": {".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_safe_rollback_connection_transaction": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._safe_rollback_connection_transaction", "name": "_safe_rollback_connection_transaction", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["connection"], "arg_types": ["sqlalchemy.engine.base.Connection"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_safe_rollback_connection_transaction", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_server_default_is_computed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["server_default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._server_default_is_computed", "name": "_server_default_is_computed", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["server_default"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_server_default_is_computed", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_server_default_is_identity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [2], "arg_names": ["server_default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._server_default_is_identity", "name": "_server_default_is_identity", "type": {".class": "CallableType", "arg_kinds": [2], "arg_names": ["server_default"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_server_default_is_identity", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_table_for_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._table_for_constraint", "name": "_table_for_constraint", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["constraint"], "arg_types": ["sqlalchemy.sql.schema.Constraint"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_table_for_constraint", "ret_type": "sqlalchemy.sql.schema.Table", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_textual_index_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["table", "text_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._textual_index_column", "name": "_textual_index_column", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["table", "text_"], "arg_types": ["sqlalchemy.sql.schema.Table", {".class": "UnionType", "items": ["builtins.str", "sqlalchemy.sql.elements.TextClause", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_textual_index_column", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_textual_index_element": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.elements.ColumnElement"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.util.sqla_compat._textual_index_element", "name": "_textual_index_element", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._textual_index_element", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.util.sqla_compat", "mro": ["alembic.util.sqla_compat._textual_index_element", "sqlalchemy.sql.elements.ColumnElement", "sqlalchemy.sql.roles.ColumnArgumentOrKeyRole", "sqlalchemy.sql.roles.ColumnArgumentRole", "sqlalchemy.sql.roles.StatementOptionRole", "sqlalchemy.sql.roles.WhereHavingRole", "sqlalchemy.sql.roles.OnClauseRole", "sqlalchemy.sql.roles.BinaryElementRole", "sqlalchemy.sql.roles.OrderByRole", "sqlalchemy.sql.roles.ColumnsClauseRole", "sqlalchemy.sql.roles.AllowsLambdaRole", "sqlalchemy.sql.roles.ByOfRole", "sqlalchemy.sql.roles.UsesInspection", "sqlalchemy.sql.roles.ColumnListRole", "sqlalchemy.sql.roles.LimitOffsetRole", "sqlalchemy.sql.roles.DMLColumnRole", "sqlalchemy.sql.roles.DDLConstraintColumnRole", "sqlalchemy.sql.roles.DDLExpressionRole", "sqlalchemy.sql.roles.StructuralRole", "sqlalchemy.sql.elements.SQLColumnExpression", "sqlalchemy.sql.elements.SQLCoreOperations", "sqlalchemy.sql.operators.ColumnOperators", "sqlalchemy.sql.operators.Operators", "sqlalchemy.sql.roles.ExpressionElementRole", "sqlalchemy.util.langhelpers.TypingOnly", "sqlalchemy.sql.roles.TypedColumnsClauseRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.DQLDMLClauseElement", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._textual_index_element.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "table", "text"], "arg_types": ["alembic.util.sqla_compat._textual_index_element", "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.elements.TextClause"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _textual_index_element", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__visit_name__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat._textual_index_element.__visit_name__", "name": "__visit_name__", "type": "builtins.str"}}, "fake_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.util.sqla_compat._textual_index_element.fake_column", "name": "fake_column", "type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "get_children": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 4], "arg_names": ["self", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._textual_index_element.get_children", "name": "get_children", "type": null}}, "table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.util.sqla_compat._textual_index_element.table", "name": "table", "type": "sqlalchemy.sql.schema.Table"}}, "text": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.util.sqla_compat._textual_index_element.text", "name": "text", "type": "sqlalchemy.sql.elements.TextClause"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.util.sqla_compat._textual_index_element.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.util.sqla_compat._textual_index_element", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_type_has_variants": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat._type_has_variants", "name": "_type_has_variants", "type": null}}, "_vers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat._vers", "name": "_vers", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.int", "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "compiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2], "arg_names": ["element", "dialects"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_mypy_only"], "fullname": "alembic.util.sqla_compat.compiles", "name": "compiles", "type": {".class": "CallableType", "arg_kinds": [0, 2], "arg_names": ["element", "dialects"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.sql.elements.ClauseElement"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compiles", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["alembic.util.sqla_compat._CompilerProtocol"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "alembic.util.sqla_compat._CompilerProtocol", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constraint_name_defined": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat.constraint_name_defined", "name": "constraint_name_defined", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constraint_name_defined", "ret_type": "builtins.bool", "type_guard": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constraint_name_or_none": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat.constraint_name_or_none", "name": "constraint_name_or_none", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constraint_name_or_none", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "constraint_name_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat.constraint_name_string", "name": "constraint_name_string", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "constraint_name_string", "ret_type": "builtins.bool", "type_guard": "builtins.str", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "identity_has_dialect_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat.identity_has_dialect_kwargs", "name": "identity_has_dialect_kwargs", "type": "builtins.bool"}}, "is_expression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["expr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat.is_expression", "name": "is_expression", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["expr"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_expression", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_expression_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["index"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.util.sqla_compat.is_expression_index", "name": "is_expression_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["index"], "arg_types": ["sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_expression_index", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sql": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql", "kind": "Gdef"}, "sqla_14_18": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat.sqla_14_18", "name": "sqla_14_18", "type": "builtins.bool"}}, "sqla_14_26": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat.sqla_14_26", "name": "sqla_14_26", "type": "builtins.bool"}}, "sqla_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat.sqla_2", "name": "sqla_2", "type": "builtins.bool"}}, "sqlalchemy_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.util.sqla_compat.sqlalchemy_version", "name": "sqlalchemy_version", "type": "builtins.str"}}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "traverse": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors.traverse", "kind": "Gdef"}, "visitors": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.visitors", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\util\\sqla_compat.py"}