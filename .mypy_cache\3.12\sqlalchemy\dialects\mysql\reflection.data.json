{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql.reflection", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DATETIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.DATETIME", "kind": "Gdef"}, "ENUM": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.enumerated.ENUM", "kind": "Gdef"}, "MySQLTableDefinitionParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser", "name": "MySQLTableDefinitionParser", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.reflection", "mro": ["sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "preparer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser.__init__", "name": "__init__", "type": null}}, "_add_option_regex": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "directive", "regex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._add_option_regex", "name": "_add_option_regex", "type": null}}, "_add_option_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._add_option_string", "name": "_add_option_string", "type": null}}, "_add_option_word": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._add_option_word", "name": "_add_option_word", "type": null}}, "_add_partition_option_word": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._add_partition_option_word", "name": "_add_partition_option_word", "type": null}}, "_check_view": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "sql"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._check_view", "name": "_check_view", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "sql"], "arg_types": ["sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_view of MySQLTableDefinitionParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_describe_to_create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "table_name", "columns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._describe_to_create", "name": "_describe_to_create", "type": null}}, "_optional_equals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._optional_equals", "name": "_optional_equals", "type": "builtins.str"}}, "_parse_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._parse_column", "name": "_parse_column", "type": null}}, "_parse_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._parse_constraints", "name": "_parse_constraints", "type": null}}, "_parse_keyexprs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "identifiers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._parse_keyexprs", "name": "_parse_keyexprs", "type": null}}, "_parse_partition_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._parse_partition_options", "name": "_parse_partition_options", "type": null}}, "_parse_table_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._parse_table_name", "name": "_parse_table_name", "type": null}}, "_parse_table_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "line", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._parse_table_options", "name": "_parse_table_options", "type": null}}, "_pr_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._pr_name", "name": "_pr_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_pr_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._pr_options", "name": "_pr_options", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_prep_regexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._prep_regexes", "name": "_prep_regexes", "type": null}}, "_re_ck_constraint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_ck_constraint", "name": "_re_ck_constraint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_column": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_column", "name": "_re_column", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_column_loose": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_column_loose", "name": "_re_column_loose", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_columns", "name": "_re_columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_csv_int": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_csv_int", "name": "_re_csv_int", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_csv_str": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_csv_str", "name": "_re_csv_str", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_fk_constraint": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_fk_constraint", "name": "_re_fk_constraint", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_is_view": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_is_view", "name": "_re_is_view", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_key": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_key", "name": "_re_key", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_key_version_sql": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_key_version_sql", "name": "_re_key_version_sql", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_keyexprs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_keyexprs", "name": "_re_keyexprs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_re_partition": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser._re_partition", "name": "_re_partition", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "dialect": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser.dialect", "name": "dialect", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "show_create", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser.parse", "name": "parse", "type": null}}, "preparer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser.preparer", "name": "preparer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.reflection.MySQLTableDefinitionParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReflectedState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState", "name": "ReflectedState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.reflection", "mro": ["sqlalchemy.dialects.mysql.reflection.ReflectedState", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.__init__", "name": "__init__", "type": null}}, "ck_constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.ck_constraints", "name": "ck_constraints", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.columns", "name": "columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "fk_constraints": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.fk_constraints", "name": "fk_constraints", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.keys", "name": "keys", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "table_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.table_name", "name": "table_name", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "table_options": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.table_options", "name": "table_options", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.reflection.ReflectedState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.reflection.ReflectedState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SET": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.enumerated.SET", "kind": "Gdef"}, "TIME": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TIME", "kind": "Gdef"}, "TIMESTAMP": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.TIMESTAMP", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.reflection.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.reflection.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.reflection.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.reflection.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.reflection.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.reflection.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_control_char_map": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.reflection._control_char_map", "name": "_control_char_map", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_control_char_regexp": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.reflection._control_char_regexp", "name": "_control_char_regexp", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_options_of_type_string": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.reflection._options_of_type_string", "name": "_options_of_type_string", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_pr_compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["regex", "cleanup"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection._pr_compile", "name": "_pr_compile", "type": null}}, "_re_compile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["regex"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection._re_compile", "name": "_re_compile", "type": null}}, "_strip_values": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["values"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection._strip_values", "name": "_strip_values", "type": null}}, "cleanup_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["raw_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.reflection.cleanup_text", "name": "cleanup_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["raw_text"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cleanup_text", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "log": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.log", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\reflection.py"}