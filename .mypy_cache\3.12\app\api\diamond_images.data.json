{".class": "MypyFile", "_fullname": "app.api.diamond_images", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ALLOWED_EXTENSIONS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond_images.ALLOWED_EXTENSIONS", "name": "ALLOWED_EXTENSIONS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "Diamond": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Diamond", "kind": "Gdef"}, "DiamondImage": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond_image.DiamondImage", "kind": "Gdef"}, "DiamondImageDetail": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond_images.DiamondImageDetail", "name": "DiamondImageDetail", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond_images.DiamondImageDetail", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond_images", "mro": ["app.api.diamond_images.DiamondImageDetail", "builtins.object"], "names": {".class": "SymbolTable", "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "diamond_id", "image_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond_images.DiamondImageDetail.delete", "name": "delete", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond_images.DiamondImageDetail.delete", "name": "delete", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "diamond_id", "image_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond_images.DiamondImageDetail.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond_images.DiamondImageDetail.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond_images.DiamondImageDetail.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond_images.DiamondImageDetail", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DiamondImageList": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.diamond_images.DiamondImageList", "name": "DiamondImageList", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.diamond_images.DiamondImageList", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.diamond_images", "mro": ["app.api.diamond_images.DiamondImageList", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "diamond_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond_images.DiamondImageList.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond_images.DiamondImageList.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "diamond_id"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.diamond_images.DiamondImageList.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.diamond_images.DiamondImageList.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.diamond_images.DiamondImageList.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.diamond_images.DiamondImageList", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Image": {".class": "SymbolTableNode", "cross_ref": "PIL.Image", "kind": "Gdef"}, "MAX_FILE_SIZE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.api.diamond_images.MAX_FILE_SIZE", "name": "MAX_FILE_SIZE", "type": "builtins.int"}}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.diamond_images.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.diamond_images.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Resource", "source_any": null, "type_of_any": 3}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond_images.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond_images.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond_images.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond_images.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond_images.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.diamond_images.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "allowed_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.diamond_images.allowed_file", "name": "allowed_file", "type": null}}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "diamond_image_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond_images.diamond_image_model", "name": "diamond_image_model", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "diamond_images_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond_images.diamond_images_ns", "name": "diamond_images_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.diamond_images.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.diamond_images.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.diamond_images.fields", "source_any": null, "type_of_any": 3}}}, "get_upload_folder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.diamond_images.get_upload_folder", "name": "get_upload_folder", "type": null}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "secure_filename": {".class": "SymbolTableNode", "cross_ref": "werkzeug.utils.secure_filename", "kind": "Gdef"}, "token_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.token_required", "kind": "Gdef"}, "uuid": {".class": "SymbolTableNode", "cross_ref": "uuid", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\diamond_images.py"}