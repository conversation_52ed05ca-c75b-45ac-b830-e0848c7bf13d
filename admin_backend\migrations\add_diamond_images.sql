-- Migration: Add diamond images table
-- Date: 2024-01-01
-- Description: Add support for diamond image management

CREATE TABLE IF NOT EXISTS diamond_images (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    diamond_id INTEGER NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    image_type VARCHAR(50) NOT NULL DEFAULT 'main',
    is_primary BOOLEAN DEFAULT FALSE,
    alt_text VARCHAR(255),
    file_size INTEGER,
    file_format VARCHAR(10),
    width INTEGER,
    height INTEGER,
    uploaded_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (diamond_id) REFERENCES diamonds (id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_diamond_images_diamond_id ON diamond_images(diamond_id);
CREATE INDEX IF NOT EXISTS idx_diamond_images_type ON diamond_images(image_type);
CREATE INDEX IF NOT EXISTS idx_diamond_images_primary ON diamond_images(is_primary);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_diamond_images_updated_at 
    AFTER UPDATE ON diamond_images
    FOR EACH ROW
BEGIN
    UPDATE diamond_images SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
