#!/usr/bin/env python3
"""
Debug diamond creation issue
"""

import requests
import json

BASE_URL = "http://localhost:8000/api"

def debug_diamond_creation():
    print("🔍 Debugging Diamond Creation...")
    
    # Login
    session = requests.Session()
    response = session.post(f"{BASE_URL}/auth/login", json={
        "email": "<EMAIL>",
        "password": "<PERSON><PERSON>@109"
    })
    
    token = response.json().get('access_token')
    session.headers.update({'Authorization': f'Bearer {token}'})
    
    # Try creating diamond without size_mm
    test_diamond = {
        "shape_id": 1,
        "carat": 1.0,
        "color": "G",
        "clarity": "SI1",
        "certificate_no": f"DEBUG_{hash('debug') % 100000:05d}",
        "vendor_id": 1,
        "purchase_date": "2025-07-21",
        "quantity": 1,
        "minimum_stock": 1,
        "status": "in_stock"
    }
    
    print(f"Sending: {json.dumps(test_diamond, indent=2)}")
    response = session.post(f"{BASE_URL}/diamonds", json=test_diamond)
    print(f"Status: {response.status_code}")
    
    try:
        response_data = response.json()
        print(f"Response: {json.dumps(response_data, indent=2)}")
    except:
        print(f"Raw response: {response.text}")

if __name__ == "__main__":
    debug_diamond_creation()
