{"data_mtime": 1753035428, "dep_lines": [15, 16, 17, 15, 7, 9, 1, 1, 1], "dep_prios": [10, 5, 5, 20, 5, 5, 5, 30, 30], "dependencies": ["sqlalchemy.sql.roles", "sqlalchemy.sql.base", "sqlalchemy.sql.schema", "sqlalchemy.sql", "__future__", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "b3e2df4f9ddd4feff8d98faa21f4fa3c369edfaf", "id": "sqlalchemy.dialects._typing", "ignore_all": true, "interface_hash": "f180c4be86c2c832a188f7b8e28e6318478a17e4", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\_typing.py", "plugin_data": null, "size": 1001, "suppressed": [], "version_id": "1.15.0"}