{".class": "MypyFile", "_fullname": "alembic.ddl.mssql", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddColumn": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.AddColumn", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ClauseElement": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.ClauseElement", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "ColumnDefault": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.ColumnDefault", "kind": "Gdef"}, "ColumnName": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.ColumnName", "kind": "Gdef"}, "ColumnNullable": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.ColumnNullable", "kind": "Gdef"}, "ColumnType": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.ColumnType", "kind": "Gdef"}, "CreateIndex": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.ddl.CreateIndex", "kind": "Gdef"}, "CursorResult": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.cursor.CursorResult", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Executable": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.base.Executable", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "MSDDLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.MSDDLCompiler", "kind": "Gdef"}, "MSSQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mssql.base.MSSQLCompiler", "kind": "Gdef"}, "MSSQLImpl": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["alembic.ddl.impl.DefaultImpl"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.mssql.MSSQLImpl", "name": "MSSQLImpl", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl", "has_param_spec_type": false, "metaclass_type": "alembic.ddl.impl.ImplMeta", "metadata": {}, "module_name": "alembic.ddl.mssql", "mro": ["alembic.ddl.mssql.MSSQLImpl", "alembic.ddl.impl.DefaultImpl", "builtins.object"], "names": {".class": "SymbolTable", "__dialect__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.mssql.MSSQLImpl.__dialect__", "name": "__dialect__", "type": "builtins.str"}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["self", "arg", "kw"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MSSQLImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compare_identity_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "metadata_identity", "inspector_identity"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl._compare_identity_default", "name": "_compare_identity_default", "type": null}}, "_exec": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "construct", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl._exec", "name": "_exec", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "construct", "args", "kw"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exec of MSSQLImpl", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.engine.cursor.CursorResult"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "adjust_reflected_dialect_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "reflected_object", "kind"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.adjust_reflected_dialect_options", "name": "adjust_reflected_dialect_options", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "reflected_object", "kind"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "adjust_reflected_dialect_options of MSSQLImpl", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "alter_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "server_default", "name", "type_", "schema", "existing_type", "existing_server_default", "existing_nullable", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.alter_column", "name": "alter_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 4], "arg_names": ["self", "table_name", "column_name", "nullable", "server_default", "name", "type_", "schema", "existing_type", "existing_server_default", "existing_nullable", "kw"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl.base._ServerDefault"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.type_api.TypeEngine"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl.base._ServerDefault"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "alter_column of MSSQLImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "batch_separator": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.mssql.MSSQLImpl.batch_separator", "name": "batch_separator", "type": "builtins.str"}}, "bulk_insert": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "table", "rows", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.bulk_insert", "name": "bulk_insert", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "table", "rows", "kw"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl", {".class": "UnionType", "items": ["sqlalchemy.sql.selectable.TableClause", "sqlalchemy.sql.schema.Table"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "bulk_insert of MSSQLImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "compare_server_default": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "inspector_column", "metadata_column", "rendered_metadata_default", "rendered_inspector_default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.compare_server_default", "name": "compare_server_default", "type": null}}, "create_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["self", "index", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.create_index", "name": "create_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["self", "index", "kw"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl", "sqlalchemy.sql.schema.Index", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_index of MSSQLImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "drop_column": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "table_name", "column", "schema", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.drop_column", "name": "drop_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 4], "arg_names": ["self", "table_name", "column", "schema", "kw"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "drop_column of MSSQLImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "emit_begin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.emit_begin", "name": "emit_begin", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emit_begin of MSSQLImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "emit_commit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.MSSQLImpl.emit_commit", "name": "emit_commit", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.ddl.mssql.MSSQLImpl"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "emit_commit of MSSQLImpl", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "identity_attrs_ignore": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.mssql.MSSQLImpl.identity_attrs_ignore", "name": "identity_attrs_ignore", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "transactional_ddl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.mssql.MSSQLImpl.transactional_ddl", "name": "transactional_ddl", "type": "builtins.bool"}}, "type_synonyms": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.mssql.MSSQLImpl.type_synonyms", "name": "type_synonyms", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.mssql.MSSQLImpl.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.mssql.MSSQLImpl", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "RenameTable": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.RenameTable", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TableClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.selectable.TableClause", "kind": "Gdef"}, "TypeEngine": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.type_api.TypeEngine", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_ExecDropConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Executable", "sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.mssql._ExecDropConstraint", "name": "_ExecDropConstraint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql._ExecDropConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.mssql", "mro": ["alembic.ddl.mssql._ExecDropConstraint", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tname", "colname", "type_", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql._ExecDropConstraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "tname", "colname", "type_", "schema"], "arg_types": ["alembic.ddl.mssql._ExecDropConstraint", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "builtins.str"], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ExecDropConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "colname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.mssql._ExecDropConstraint.colname", "name": "colname", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, "builtins.str"], "uses_pep604_syntax": false}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.mssql._ExecDropConstraint.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.mssql._ExecDropConstraint.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.mssql._ExecDropConstraint.tname", "name": "tname", "type": "builtins.str"}}, "type_": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.mssql._ExecDropConstraint.type_", "name": "type_", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.mssql._ExecDropConstraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.mssql._ExecDropConstraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ExecDropFKConstraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.base.Executable", "sqlalchemy.sql.elements.ClauseElement"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl.mssql._ExecDropFKConstraint", "name": "_ExecDropFKConstraint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql._ExecDropFKConstraint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl.mssql", "mro": ["alembic.ddl.mssql._ExecDropFKConstraint", "sqlalchemy.sql.base.Executable", "sqlalchemy.sql.roles.StatementRole", "sqlalchemy.sql.roles.SQLRole", "sqlalchemy.sql.elements.ClauseElement", "sqlalchemy.sql.annotation.SupportsWrappingAnnotations", "sqlalchemy.sql.annotation.SupportsAnnotations", "sqlalchemy.sql.cache_key.MemoizedHasCacheKey", "sqlalchemy.sql.cache_key.HasCacheKey", "sqlalchemy.util.langhelpers.HasMemoized", "sqlalchemy.sql.traversals.HasCopyInternals", "sqlalchemy.sql.visitors.ExternallyTraversible", "sqlalchemy.sql.visitors.HasTraverseInternals", "sqlalchemy.sql.elements.CompilerElement", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tname", "colname", "schema"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql._ExecDropFKConstraint.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "tname", "colname", "schema"], "arg_types": ["alembic.ddl.mssql._ExecDropFKConstraint", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ExecDropFKConstraint", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "colname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.mssql._ExecDropFKConstraint.colname", "name": "colname", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}}}, "inherit_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl.mssql._ExecDropFKConstraint.inherit_cache", "name": "inherit_cache", "type": "builtins.bool"}}, "schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.mssql._ExecDropFKConstraint.schema", "name": "schema", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "tname": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl.mssql._ExecDropFKConstraint.tname", "name": "tname", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl.mssql._ExecDropFKConstraint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl.mssql._ExecDropFKConstraint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ServerDefault": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base._ServerDefault", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.mssql.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.mssql.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.mssql.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.mssql.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.mssql.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl.mssql.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_exec_drop_col_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql._exec_drop_col_constraint", "name": "_exec_drop_col_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.mssql._ExecDropConstraint", "sqlalchemy.dialects.mssql.base.MSSQLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exec_drop_col_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql._exec_drop_col_constraint", "name": "_exec_drop_col_constraint", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "_exec_drop_col_fk_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql._exec_drop_col_fk_constraint", "name": "_exec_drop_col_fk_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.mssql._ExecDropFKConstraint", "sqlalchemy.dialects.mssql.base.MSSQLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exec_drop_col_fk_constraint", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql._exec_drop_col_fk_constraint", "name": "_exec_drop_col_fk_constraint", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "alter_column": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.alter_column", "kind": "Gdef"}, "alter_table": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.alter_table", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "compiles": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat.compiles", "kind": "Gdef"}, "format_column_name": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_column_name", "kind": "Gdef"}, "format_server_default": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_server_default", "kind": "Gdef"}, "format_table_name": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_table_name", "kind": "Gdef"}, "format_type": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.base.format_type", "kind": "Gdef"}, "mssql_add_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["compiler", "column", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl.mssql.mssql_add_column", "name": "mssql_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["compiler", "column", "kw"], "arg_types": ["sqlalchemy.dialects.mssql.base.MSDDLCompiler", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mssql_add_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}, "visit_add_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql.visit_add_column", "name": "visit_add_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.AddColumn", "sqlalchemy.dialects.mssql.base.MSDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_add_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql.visit_add_column", "name": "visit_add_column", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "visit_column_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql.visit_column_default", "name": "visit_column_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnDefault", "sqlalchemy.dialects.mssql.base.MSDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_default", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql.visit_column_default", "name": "visit_column_default", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "visit_column_nullable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql.visit_column_nullable", "name": "visit_column_nullable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnNullable", "sqlalchemy.dialects.mssql.base.MSDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_nullable", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql.visit_column_nullable", "name": "visit_column_nullable", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "visit_column_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql.visit_column_type", "name": "visit_column_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnType", "sqlalchemy.dialects.mssql.base.MSDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_column_type", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql.visit_column_type", "name": "visit_column_type", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "visit_rename_column": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql.visit_rename_column", "name": "visit_rename_column", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.ColumnName", "sqlalchemy.dialects.mssql.base.MSDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_rename_column", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql.visit_rename_column", "name": "visit_rename_column", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}, "visit_rename_table": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl.mssql.visit_rename_table", "name": "visit_rename_table", "type": {".class": "CallableType", "arg_kinds": [0, 0, 4], "arg_names": ["element", "compiler", "kw"], "arg_types": ["alembic.ddl.base.RenameTable", "sqlalchemy.dialects.mssql.base.MSDDLCompiler", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_rename_table", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.ddl.mssql.visit_rename_table", "name": "visit_rename_table", "type": "alembic.util.sqla_compat._CompilerProtocol"}}}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\ddl\\mssql.py"}