{".class": "MypyFile", "_fullname": "alembic.operations", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AbstractOperations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.AbstractOperations", "kind": "Gdef"}, "BatchOperations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.BatchOperations", "kind": "Gdef"}, "MigrateOperation": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrateOperation", "kind": "Gdef"}, "MigrationScript": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrationScript", "kind": "Gdef"}, "Operations": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.base.Operations", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.operations.__all__", "name": "__all__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.operations.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "toimpl": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.toimpl", "kind": "Gdef", "module_public": false}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\operations\\__init__.py"}