{".class": "MypyFile", "_fullname": "app.utils.decorators", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIError": {".class": "SymbolTableNode", "cross_ref": "app.utils.error_handler.APIError", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "app.models.user.User", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.decorators.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.decorators.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.decorators.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.decorators.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.decorators.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.utils.decorators.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "admin_required": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.decorators.admin_required", "name": "admin_required", "type": null}}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "get_jwt_identity": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.utils.get_jwt_identity", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "role_required": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["roles"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.decorators.role_required", "name": "role_required", "type": null}}, "token_required": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fn"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.utils.decorators.token_required", "name": "token_required", "type": null}}, "verify_jwt_in_request": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.view_decorators.verify_jwt_in_request", "kind": "Gdef"}, "wraps": {".class": "SymbolTableNode", "cross_ref": "functools.wraps", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\utils\\decorators.py"}