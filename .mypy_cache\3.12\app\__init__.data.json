{".class": "MypyFile", "_fullname": "app", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Bcrypt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.Bcrypt", "name": "Bcrypt", "type": {".class": "AnyType", "missing_import_name": "app.Bcrypt", "source_any": null, "type_of_any": 3}}}, "CORS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.CORS", "name": "CORS", "type": {".class": "AnyType", "missing_import_name": "app.CORS", "source_any": null, "type_of_any": 3}}}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "JWTManager": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.jwt_manager.JWTManager", "kind": "Gdef"}, "Migrate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.Migrate", "name": "Migrate", "type": {".class": "AnyType", "missing_import_name": "app.Migrate", "source_any": null, "type_of_any": 3}}}, "SQLAlchemy": {".class": "SymbolTableNode", "cross_ref": "flask_sqlalchemy.extension.SQLAlchemy", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.app", "name": "app", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "bcrypt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.bcrypt", "name": "bcrypt", "type": {".class": "AnyType", "missing_import_name": "app.Bcrypt", "source_any": {".class": "AnyType", "missing_import_name": "app.Bcrypt", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "check_if_token_revoked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["jwt_header", "jwt_payload"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.check_if_token_revoked", "name": "check_if_token_revoked", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.check_if_token_revoked", "name": "check_if_token_revoked", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "check_if_token_revoked", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "create_admin_user": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.create_admin_user", "name": "create_admin_user", "type": null}}, "create_app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["config_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.create_app", "name": "create_app", "type": null}}, "db": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.db", "name": "db", "type": "flask_sqlalchemy.extension.SQLAlchemy"}}, "expired_token_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["jwt_header", "jwt_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.expired_token_callback", "name": "expired_token_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.expired_token_callback", "name": "expired_token_callback", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "expired_token_callback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "invalid_token_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.invalid_token_callback", "name": "invalid_token_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.invalid_token_callback", "name": "invalid_token_callback", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "invalid_token_callback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "jwt": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.jwt", "name": "jwt", "type": "flask_jwt_extended.jwt_manager.JWTManager"}}, "load_dotenv": {".class": "SymbolTableNode", "cross_ref": "dotenv.main.load_dotenv", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "app.utils.logger.logger", "kind": "Gdef"}, "migrate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.migrate", "name": "migrate", "type": {".class": "AnyType", "missing_import_name": "app.Migrate", "source_any": {".class": "AnyType", "missing_import_name": "app.Migrate", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "needs_fresh_token_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["jwt_header", "jwt_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.needs_fresh_token_callback", "name": "needs_fresh_token_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.needs_fresh_token_callback", "name": "needs_fresh_token_callback", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "needs_fresh_token_callback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "revoked_token_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["jwt_header", "jwt_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.revoked_token_callback", "name": "revoked_token_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.revoked_token_callback", "name": "revoked_token_callback", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "revoked_token_callback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "unauthorized_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.unauthorized_callback", "name": "unauthorized_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.unauthorized_callback", "name": "unauthorized_callback", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "unauthorized_callback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_identity_lookup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.user_identity_lookup", "name": "user_identity_lookup", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.user_identity_lookup", "name": "user_identity_lookup", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "user_identity_lookup", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "user_lookup_callback": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_jwt_header", "jwt_data"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.user_lookup_callback", "name": "user_lookup_callback", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.user_lookup_callback", "name": "user_lookup_callback", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "user_lookup_callback", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "path": "E:\\admin_panel\\admin_backend\\app\\__init__.py"}