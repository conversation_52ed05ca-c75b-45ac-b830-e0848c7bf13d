{"data_mtime": 1753035434, "dep_lines": [13, 17, 20, 24, 25, 18, 19, 9, 11, 18, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.connectors.asyncio", "sqlalchemy.connectors.pyodbc", "sqlalchemy.util.concurrency", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.url", "sqlalchemy.pool", "sqlalchemy.util", "__future__", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "sqlalchemy.engine", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.util._py_collections"], "hash": "8949c45b3ba9b2f84639be239f72000e4e61cea9", "id": "sqlalchemy.connectors.aioodbc", "ignore_all": true, "interface_hash": "99257634814717bc94d18644876da5e1755f064e", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\connectors\\aioodbc.py", "plugin_data": null, "size": 5462, "suppressed": [], "version_id": "1.15.0"}