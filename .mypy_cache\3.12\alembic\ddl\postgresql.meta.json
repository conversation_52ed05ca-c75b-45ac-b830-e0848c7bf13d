{"data_mtime": 1753035443, "dep_lines": [61, 62, 63, 64, 26, 30, 32, 35, 44, 47, 48, 49, 50, 52, 69, 71, 74, 76, 25, 29, 46, 47, 48, 4, 6, 7, 8, 18, 46, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 25, 25, 25, 25, 5, 5, 10, 20, 20, 5, 10, 10, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.array", "sqlalchemy.dialects.postgresql.base", "sqlalchemy.dialects.postgresql.hstore", "sqlalchemy.dialects.postgresql.json", "sqlalchemy.dialects.postgresql", "sqlalchemy.sql.elements", "sqlalchemy.sql.functions", "alembic.ddl.base", "alembic.ddl.impl", "alembic.autogenerate.render", "alembic.operations.ops", "alembic.operations.schemaobj", "alembic.operations.base", "alembic.util.sqla_compat", "sqlalchemy.sql.schema", "sqlalchemy.sql.type_api", "alembic.autogenerate.api", "alembic.runtime.migration", "sqlalchemy.types", "sqlalchemy.schema", "alembic.util", "alembic.autogenerate", "alembic.operations", "__future__", "logging", "re", "typing", "sqlalchemy", "alembic", "builtins", "_frozen_importlib", "abc", "alembic.ddl._autogen", "alembic.operations.batch", "alembic.runtime", "alembic.util.langhelpers", "enum", "sqlalchemy.dialects", "sqlalchemy.dialects.postgresql.ext", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.ddl", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.selectable", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "0a6363bd67d2bf789014289690533eec9f08eebf", "id": "alembic.ddl.postgresql", "ignore_all": true, "interface_hash": "3ed37638c76c1105ad39560ca0e02cf0ae4ae7e6", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\ddl\\postgresql.py", "plugin_data": null, "size": 29950, "suppressed": [], "version_id": "1.15.0"}