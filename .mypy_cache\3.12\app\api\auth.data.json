{".class": "MypyFile", "_fullname": "app.api.auth", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIError": {".class": "SymbolTableNode", "cross_ref": "app.utils.error_handler.APIError", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "app.config.Config", "kind": "Gdef"}, "FreshTokenRequired": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.FreshTokenRequired", "kind": "Gdef"}, "InvalidHeaderError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.InvalidHeaderError", "kind": "Gdef"}, "Login": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.auth.Login", "name": "<PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.auth.Login", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.auth", "mro": ["app.api.auth.Login", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.Login.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.auth.Login.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.auth.Login.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.auth.Login", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Logout": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.auth.Logout", "name": "Logout", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.auth.Logout", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.auth", "mro": ["app.api.auth.Logout", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.Logout.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.auth.Logout.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.auth.Logout.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.auth.Logout", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MAX_LOGIN_ATTEMPTS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.MAX_LOGIN_ATTEMPTS", "name": "MAX_LOGIN_ATTEMPTS", "type": "builtins.int"}}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.auth.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}}}, "NoAuthorizationError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.NoAuthorizationError", "kind": "Gdef"}, "REFRESH_TOKEN_EXPIRY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.REFRESH_TOKEN_EXPIRY", "name": "REFRESH_TOKEN_EXPIRY", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "Refresh": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.auth.Refresh", "name": "Refresh", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.auth.Refresh", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.auth", "mro": ["app.api.auth.Refresh", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.Refresh.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.auth.Refresh.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.auth.Refresh.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.auth.Refresh", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Register": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.auth.Register", "name": "Register", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.auth.Register", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.auth", "mro": ["app.api.auth.Register", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.Register.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.auth.Register.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.auth.Register.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.auth.Register", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.auth.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Resource", "source_any": null, "type_of_any": 3}}}, "RevokedTokenError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.RevokedTokenError", "kind": "Gdef"}, "TOKEN_EXPIRY": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.TOKEN_EXPIRY", "name": "TOKEN_EXPIRY", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "TokenBlacklist": {".class": "SymbolTableNode", "cross_ref": "app.models.token.TokenBlacklist", "kind": "Gdef"}, "User": {".class": "SymbolTableNode", "cross_ref": "app.models.user.User", "kind": "Gdef"}, "UserProfile": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.auth.UserProfile", "name": "UserProfile", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.auth.UserProfile", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.auth", "mro": ["app.api.auth.UserProfile", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.UserProfile.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.auth.UserProfile.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.UserProfile.put", "name": "put", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.auth.UserProfile.put", "name": "put", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.auth.UserProfile.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.auth.UserProfile", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "UserSchema": {".class": "SymbolTableNode", "cross_ref": "app.models.user.UserSchema", "kind": "Gdef"}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "marshmallow.exceptions.ValidationError", "kind": "Gdef"}, "WrongTokenError": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.exceptions.WrongTokenError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.auth.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.auth.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.auth.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.auth.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.auth.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.auth.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "auth_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.auth_ns", "name": "auth_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "create_access_token": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.utils.create_access_token", "kind": "Gdef"}, "create_refresh_token": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.utils.create_refresh_token", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.auth.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.fields", "source_any": null, "type_of_any": 3}}}, "get_jwt": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.utils.get_jwt", "kind": "Gdef"}, "get_jwt_identity": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.utils.get_jwt_identity", "kind": "Gdef"}, "handle_generic_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.handle_generic_error", "name": "handle_generic_error", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.api.auth.handle_generic_error", "name": "handle_generic_error", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "handle_jwt_error": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.auth.handle_jwt_error", "name": "handle_jwt_error", "type": null}}, "handle_jwt_errors": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["error"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.auth.handle_jwt_errors", "name": "handle_jwt_errors", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "app.api.auth.handle_jwt_errors", "name": "handle_jwt_errors", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "jwt_required": {".class": "SymbolTableNode", "cross_ref": "flask_jwt_extended.view_decorators.jwt_required", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "cross_ref": "app.utils.logger.logger", "kind": "Gdef"}, "login_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.login_model", "name": "login_model", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "register_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.register_model", "name": "register_model", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "token_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.auth.token_model", "name": "token_model", "type": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.auth.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "validate_email": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["email"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.auth.validate_email", "name": "validate_email", "type": null}}, "validate_password": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["password"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "app.api.auth.validate_password", "name": "validate_password", "type": null}}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\auth.py"}