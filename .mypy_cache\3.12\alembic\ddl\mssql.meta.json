{"data_mtime": 1753035443, "dep_lines": [40, 17, 18, 20, 32, 34, 42, 43, 45, 46, 14, 15, 33, 4, 6, 7, 14, 33, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 5, 5, 5, 5, 5, 25, 25, 25, 25, 10, 5, 10, 5, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.mssql.base", "sqlalchemy.sql.base", "sqlalchemy.sql.elements", "alembic.ddl.base", "alembic.ddl.impl", "alembic.util.sqla_compat", "sqlalchemy.engine.cursor", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "sqlalchemy.types", "sqlalchemy.schema", "alembic.util", "__future__", "re", "typing", "sqlalchemy", "alembic", "builtins", "_frozen_importlib", "abc", "sqlalchemy.dialects", "sqlalchemy.dialects.mssql", "sqlalchemy.engine", "sqlalchemy.engine.base", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.result", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.ddl", "sqlalchemy.sql.functions", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers"], "hash": "1df3c6b37cd4b7d068078ceae208b1f8f45a9d52", "id": "alembic.ddl.mssql", "ignore_all": true, "interface_hash": "623d604ff6c20619659d36df99839aa350551768", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\ddl\\mssql.py", "plugin_data": null, "size": 14216, "suppressed": [], "version_id": "1.15.0"}