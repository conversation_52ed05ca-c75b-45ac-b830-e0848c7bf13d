{".class": "MypyFile", "_fullname": "configparser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": true, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_hidden": true, "module_public": false}, "BasicInterpolation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Interpolation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.BasicInterpolation", "name": "BasicInterpolation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.BasicInterpolation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.BasicInterpolation", "configparser.Interpolation", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.BasicInterpolation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.BasicInterpolation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "ConfigParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.RawConfigParser"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.ConfigParser", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.ConfigParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "configparser", "mro": ["configparser.ConfigParser", "configparser.RawConfigParser", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.ConfigParser.get", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.ConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.ConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Config<PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.ConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.ConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Config<PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.ConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.ConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Config<PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.ConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.ConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Config<PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.ConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Config<PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.ConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of Config<PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.ConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.ConfigParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.ConfigParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConverterMapping": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConverterCallback"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.ConverterMapping", "name": "ConverterMapping", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.ConverterMapping", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "configparser", "mro": ["configparser.ConverterMapping", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "GETTERCRE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.ConverterMapping.GETTERCRE", "name": "GETTERCRE", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ConverterMapping.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.ConverterMapping", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of ConverterMapping", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ConverterMapping.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.ConverterMapping", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of ConverterMapping", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConverterCallback"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ConverterMapping.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "parser"], "arg_types": ["configparser.ConverterMapping", "configparser.RawConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ConverterMapping", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ConverterMapping.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["configparser.ConverterMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of ConverterMapping", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ConverterMapping.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["configparser.ConverterMapping"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of ConverterMapping", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ConverterMapping.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["configparser.ConverterMapping", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConverterCallback"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of ConverterMapping", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.ConverterMapping.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.ConverterMapping", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DEFAULTSECT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": "DEFAULT", "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "configparser.DEFAULTSECT", "name": "DEFAULTSECT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "DEFAULT"}, "type_ref": "builtins.str"}}}, "DuplicateOptionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.DuplicateOptionError", "name": "DuplicateOptionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.DuplicateOptionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.DuplicateOptionError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "section", "option", "source", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.DuplicateOptionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "section", "option", "source", "lineno"], "arg_types": ["configparser.DuplicateOptionError", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DuplicateOptionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.DuplicateOptionError.lineno", "name": "lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.DuplicateOptionError.option", "name": "option", "type": "builtins.str"}}, "section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.DuplicateOptionError.section", "name": "section", "type": "builtins.str"}}, "source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.DuplicateOptionError.source", "name": "source", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.DuplicateOptionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.DuplicateOptionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DuplicateSectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.DuplicateSectionError", "name": "DuplicateSectionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.DuplicateSectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.DuplicateSectionError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "section", "source", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.DuplicateSectionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "section", "source", "lineno"], "arg_types": ["configparser.DuplicateSectionError", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DuplicateSectionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.DuplicateSectionError.lineno", "name": "lineno", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.DuplicateSectionError.section", "name": "section", "type": "builtins.str"}}, "source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.DuplicateSectionError.source", "name": "source", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.DuplicateSectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.DuplicateSectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Error": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.Error", "name": "Error", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.Error", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.Error.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "msg"], "arg_types": ["configparser.Error", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Error", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.Error.message", "name": "message", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.Error.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.Error", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ExtendedInterpolation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Interpolation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.ExtendedInterpolation", "name": "ExtendedInterpolation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.ExtendedInterpolation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.ExtendedInterpolation", "configparser.Interpolation", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.ExtendedInterpolation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.ExtendedInterpolation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Final": {".class": "SymbolTableNode", "cross_ref": "typing.Final", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Interpolation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.Interpolation", "name": "Interpolation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.Interpolation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.Interpolation", "builtins.object"], "names": {".class": "SymbolTable", "before_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value", "defaults"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.Interpolation.before_get", "name": "before_get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value", "defaults"], "arg_types": ["configparser.Interpolation", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._<PERSON>rser"}, "builtins.str", "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_get of Interpolation", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "before_read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.Interpolation.before_read", "name": "before_read", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value"], "arg_types": ["configparser.Interpolation", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._<PERSON>rser"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_read of Interpolation", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "before_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.Interpolation.before_set", "name": "before_set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value"], "arg_types": ["configparser.Interpolation", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._<PERSON>rser"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_set of Interpolation", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "before_write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.Interpolation.before_write", "name": "before_write", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value"], "arg_types": ["configparser.Interpolation", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._<PERSON>rser"}, "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_write of Interpolation", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.Interpolation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.Interpolation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InterpolationDepthError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.InterpolationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.InterpolationDepthError", "name": "InterpolationDepthError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.InterpolationDepthError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.InterpolationDepthError", "configparser.InterpolationError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "option", "section", "rawval"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.InterpolationDepthError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "option", "section", "rawval"], "arg_types": ["configparser.InterpolationDepthError", "builtins.str", "builtins.str", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InterpolationDepthError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.InterpolationDepthError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.InterpolationDepthError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InterpolationError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.InterpolationError", "name": "InterpolationError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.InterpolationError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.InterpolationError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "option", "section", "msg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.InterpolationError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "option", "section", "msg"], "arg_types": ["configparser.InterpolationError", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InterpolationError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.InterpolationError.option", "name": "option", "type": "builtins.str"}}, "section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.InterpolationError.section", "name": "section", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.InterpolationError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.InterpolationError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InterpolationMissingOptionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.InterpolationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.InterpolationMissingOptionError", "name": "InterpolationMissingOptionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.InterpolationMissingOptionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.InterpolationMissingOptionError", "configparser.InterpolationError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "option", "section", "rawval", "reference"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.InterpolationMissingOptionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "option", "section", "rawval", "reference"], "arg_types": ["configparser.InterpolationMissingOptionError", "builtins.str", "builtins.str", "builtins.object", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of InterpolationMissingOptionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.InterpolationMissingOptionError.reference", "name": "reference", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.InterpolationMissingOptionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.InterpolationMissingOptionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "InterpolationSyntaxError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.InterpolationError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.InterpolationSyntaxError", "name": "InterpolationSyntaxError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.InterpolationSyntaxError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.InterpolationSyntaxError", "configparser.InterpolationError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.InterpolationSyntaxError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.InterpolationSyntaxError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ItemsView": {".class": "SymbolTableNode", "cross_ref": "typing.ItemsView", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_hidden": true, "module_public": false}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef", "module_hidden": true, "module_public": false}, "LegacyInterpolation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Interpolation"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.LegacyInterpolation", "name": "LegacyInterpolation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.LegacyInterpolation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.LegacyInterpolation", "configparser.Interpolation", "builtins.object"], "names": {".class": "SymbolTable", "before_get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.LegacyInterpolation.before_get", "name": "before_get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["self", "parser", "section", "option", "value", "vars"], "arg_types": ["configparser.LegacyInterpolation", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._<PERSON>rser"}, "builtins.str", "builtins.str", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "before_get of LegacyInterpolation", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.LegacyInterpolation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.LegacyInterpolation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MAX_INTERPOLATION_DEPTH": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "final_value": 10, "flags": ["is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "configparser.MAX_INTERPOLATION_DEPTH", "name": "MAX_INTERPOLATION_DEPTH", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.int", "value": 10}, "type_ref": "builtins.int"}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MaybeNone": {".class": "SymbolTableNode", "cross_ref": "_typeshed.MaybeNone", "kind": "Gdef", "module_hidden": true, "module_public": false}, "MissingSectionHeaderError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.ParsingError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.MissingSectionHeaderError", "name": "MissingSectionHeaderError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.MissingSectionHeaderError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.MissingSectionHeaderError", "configparser.ParsingError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "filename", "lineno", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.MissingSectionHeaderError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "filename", "lineno", "line"], "arg_types": ["configparser.MissingSectionHeaderError", "builtins.str", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MissingSectionHeaderError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "line": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.MissingSectionHeaderError.line", "name": "line", "type": "builtins.str"}}, "lineno": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.MissingSectionHeaderError.lineno", "name": "lineno", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.MissingSectionHeaderError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.MissingSectionHeaderError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MutableMapping": {".class": "SymbolTableNode", "cross_ref": "typing.MutableMapping", "kind": "Gdef", "module_hidden": true, "module_public": false}, "NoOptionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.NoOptionError", "name": "NoOptionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.NoOptionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.NoOptionError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "option", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.NoOptionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "option", "section"], "arg_types": ["configparser.NoOptionError", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoOptionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.NoOptionError.option", "name": "option", "type": "builtins.str"}}, "section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.NoOptionError.section", "name": "section", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.NoOptionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.NoOptionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoSectionError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.NoSectionError", "name": "NoSectionError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.NoSectionError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.NoSectionError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.NoSectionError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "section"], "arg_types": ["configparser.NoSectionError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of NoSectionError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.NoSectionError.section", "name": "section", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.NoSectionError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.NoSectionError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParsingError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["configparser.Error"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.ParsingError", "name": "ParsingE<PERSON>r", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.ParsingError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "configparser", "mro": ["configparser.ParsingError", "configparser.Error", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ParsingError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "source"], "arg_types": ["configparser.ParsingError", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ParsingError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "append": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "lineno", "line"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.ParsingError.append", "name": "append", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "lineno", "line"], "arg_types": ["configparser.ParsingError", "builtins.int", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "append of Parsing<PERSON><PERSON>r", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.ParsingError.errors", "name": "errors", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.ParsingError.source", "name": "source", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.ParsingError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.ParsingError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef", "module_hidden": true, "module_public": false}, "RawConfigParser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.RawConfigParser", "name": "RawConfig<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "configparser", "mro": ["configparser.RawConfigParser", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "BOOLEAN_STATES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.RawConfigParser.BOOLEAN_STATES", "name": "BOOLEAN_STATES", "type": {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "NONSPACECRE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.RawConfigParser.NONSPACECRE", "name": "NONSPACECRE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "OPTCRE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.RawConfigParser.OPTCRE", "name": "OPTCRE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "OPTCRE_NV": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.RawConfigParser.OPTCRE_NV", "name": "OPTCRE_NV", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "SECTCRE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.RawConfigParser.SECTCRE", "name": "SECTCRE", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}}}, "_OPT_NV_TMPL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.RawConfigParser._OPT_NV_TMPL", "name": "_OPT_NV_TMPL", "type": "builtins.str"}}, "_OPT_TMPL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.RawConfigParser._OPT_TMPL", "name": "_OPT_TMPL", "type": "builtins.str"}}, "_SECT_TMPL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready"], "fullname": "configparser.RawConfigParser._SECT_TMPL", "name": "_SECT_TMPL", "type": "builtins.str"}}, "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.RawConfigParser", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of RawConfigParser", "ret_type": "configparser.SectionProxy", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.__init__", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1, 3, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "defaults", "dict_type", "allow_no_value", "delimiters", "comment_prefixes", "inline_comment_prefixes", "strict", "empty_lines_in_values", "default_section", "interpolation", "converters"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeType", "item": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}, "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", "builtins.str", {".class": "UnionType", "items": ["configparser.Interpolation", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConvertersMap"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["configparser.RawConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of RawConfigParser", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["configparser.RawConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of RawConfigParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["configparser.RawConfigParser", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_conv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "conv", "raw", "vars", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser._get_conv", "name": "_get_conv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "conv", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser._get_conv", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser._get_conv", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_conv of RawConfigParser", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser._get_conv", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser._get_conv", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "add_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.add_section", "name": "add_section", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "section"], "arg_types": ["configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_section of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "converters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "configparser.RawConfigParser.converters", "name": "converters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.RawConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "converters of RawConfigParser", "ret_type": "configparser.ConverterMapping", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.converters", "name": "converters", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.RawConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "converters of RawConfigParser", "ret_type": "configparser.ConverterMapping", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "default_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "configparser.RawConfigParser.default_section", "name": "default_section", "type": "builtins.str"}}, "defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.defaults", "name": "defaults", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.RawConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "defaults of RawConfigParser", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.get", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 3], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.MaybeNone"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "getboolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.getboolean", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "getfloat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.getfloat", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of RawConfigParser", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of RawConfigParser", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of RawConfigParser", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "getint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.getint", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of RawConfigParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of RawConfigParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of RawConfigParser", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5], "arg_names": ["self", "section", "option", "raw", "vars", "fallback"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of RawConfigParser", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.RawConfigParser.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "has_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.has_option", "name": "has_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "option"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_option of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.has_section", "name": "has_section", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "section"], "arg_types": ["configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_section of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "items": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.items", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of RawConfigParser", "ret_type": {".class": "Instance", "args": ["builtins.str", "configparser.SectionProxy"], "extra_attrs": null, "type_ref": "typing.ItemsView"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of RawConfigParser", "ret_type": {".class": "Instance", "args": ["builtins.str", "configparser.SectionProxy"], "extra_attrs": null, "type_ref": "typing.ItemsView"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "section", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.RawConfigParser.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "section", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of RawConfigParser", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.RawConfigParser.items", "name": "items", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "section", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of RawConfigParser", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of RawConfigParser", "ret_type": {".class": "Instance", "args": ["builtins.str", "configparser.SectionProxy"], "extra_attrs": null, "type_ref": "typing.ItemsView"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "section", "raw", "vars"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "items of RawConfigParser", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.options", "name": "options", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "section"], "arg_types": ["configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "options of RawConfigParser", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "optionxform": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "optionstr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.optionxform", "name": "optionxform", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "optionstr"], "arg_types": ["configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optionxform of RawConfigParser", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "filenames", "encoding"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "filenames", "encoding"], "arg_types": ["configparser.RawConfigParser", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "_typeshed.StrOrBytesPath"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of RawConfigP<PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "dictionary", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.read_dict", "name": "read_dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "dictionary", "source"], "arg_types": ["configparser.RawConfigParser", {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_dict of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "f", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.read_file", "name": "read_file", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "f", "source"], "arg_types": ["configparser.RawConfigParser", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_file of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read_string": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "source"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.read_string", "name": "read_string", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "string", "source"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read_string of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_option": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "option"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.remove_option", "name": "remove_option", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "section", "option"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_option of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "remove_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "section"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.remove_section", "name": "remove_section", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "section"], "arg_types": ["configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "remove_section of RawConfigParser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.sections", "name": "sections", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.RawConfigParser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sections of RawConfigParser", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "section", "option", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.set", "name": "set", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "section", "option", "value"], "arg_types": ["configparser.RawConfigParser", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set of RawConfigParser", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "fp", "space_around_delimiters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.RawConfigParser.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "fp", "space_around_delimiters"], "arg_types": ["configparser.RawConfigParser", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "_typeshed.SupportsWrite"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of <PERSON><PERSON>onfi<PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.RawConfigParser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.RawConfigParser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SectionProxy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.MutableMapping"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "configparser.SectionProxy", "name": "SectionProxy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "configparser", "mro": ["configparser.SectionProxy", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__contains__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__contains__", "name": "__contains__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.SectionProxy", "builtins.object"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__contains__ of SectionProxy", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__delitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__delitem__", "name": "__delitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.SectionProxy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__delitem__ of SectionProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.SectionProxy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of SectionProxy", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__getitem__", "name": "__getitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["configparser.SectionProxy", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getitem__ of SectionProxy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "parser", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "parser", "name"], "arg_types": ["configparser.SectionProxy", "configparser.RawConfigParser", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of SectionProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["configparser.SectionProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of SectionProxy", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__len__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__len__", "name": "__len__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["configparser.SectionProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__len__ of SectionProxy", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__setitem__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.__setitem__", "name": "__setitem__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__setitem__ of SectionProxy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.get", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "raw", "vars", "_impl", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "raw", "vars", "_impl", "kwargs"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "raw", "vars", "_impl", "kwargs"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "fallback", "raw", "vars", "_impl", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "fallback", "raw", "vars", "_impl", "kwargs"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "fallback", "raw", "vars", "_impl", "kwargs"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "raw", "vars", "_impl", "kwargs"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 0, 5, 5, 5, 4], "arg_names": ["self", "option", "fallback", "raw", "vars", "_impl", "kwargs"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.get", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "getboolean": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.getboolean", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.getboolean", "name": "getboolean", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getboolean of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getboolean", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "getfloat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.getfloat", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.getfloat", "name": "getfloat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getfloat of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.float", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getfloat", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "getint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "configparser.SectionProxy.getint", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "configparser.SectionProxy.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.getint", "name": "getint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["self", "option", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 5, 5], "arg_names": ["self", "option", "fallback", "raw", "vars"], "arg_types": ["configparser.SectionProxy", "builtins.str", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}, "builtins.bool", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "getint of SectionProxy", "ret_type": {".class": "UnionType", "items": ["builtins.int", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "id": -1, "name": "_T", "namespace": "configparser.SectionProxy.getint", "upper_bound": "builtins.object", "values": [], "variance": 0}]}]}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "configparser.SectionProxy.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.SectionProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of SectionProxy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.SectionProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of SectionProxy", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "parser": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "configparser.SectionProxy.parser", "name": "parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.SectionProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parser of SectionProxy", "ret_type": "configparser.RawConfigParser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "configparser.SectionProxy.parser", "name": "parser", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["configparser.SectionProxy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parser of SectionProxy", "ret_type": "configparser.RawConfigParser", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser.SectionProxy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "configparser.SectionProxy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_hidden": true, "module_public": false}, "StrOrBytesPath": {".class": "SymbolTableNode", "cross_ref": "_typeshed.StrOrBytesPath", "kind": "Gdef", "module_hidden": true, "module_public": false}, "SupportsWrite": {".class": "SymbolTableNode", "cross_ref": "_typeshed.SupportsWrite", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef", "module_hidden": true, "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_hidden": true, "module_public": false}, "_ConverterCallback": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "configparser._ConverterCallback", "line": 82, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ConvertersMap": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "configparser._ConvertersMap", "line": 83, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._ConverterCallback"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_Parser": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "configparser._<PERSON>rser", "line": 81, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "configparser._Section"}], "extra_attrs": null, "type_ref": "typing.MutableMapping"}}}, "_Section": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "configparser._Section", "line": 80, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "configparser._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "configparser.__all__", "name": "__all__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "configparser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "configparser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "configparser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "configparser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "configparser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "configparser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_hidden": true, "module_public": false}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef", "module_hidden": true, "module_public": false}}, "path": "c:\\Users\\<USER>\\.vscode\\extensions\\ms-python.mypy-type-checker-2025.2.0\\bundled\\libs\\mypy\\typeshed\\stdlib\\configparser.pyi"}