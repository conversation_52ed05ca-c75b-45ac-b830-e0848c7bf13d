{".class": "MypyFile", "_fullname": "app.api.dashboard", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dashboard": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.dashboard.Dashboard", "name": "Dashboard", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.dashboard.Dashboard", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.dashboard", "mro": ["app.api.dashboard.Dashboard", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.dashboard.Dashboard.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.Dashboard.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.dashboard.Dashboard.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.dashboard.Dashboard", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DashboardActivity": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.dashboard.DashboardActivity", "name": "DashboardActivity", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.dashboard.DashboardActivity", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.dashboard", "mro": ["app.api.dashboard.DashboardActivity", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.dashboard.DashboardActivity.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.DashboardActivity.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.dashboard.DashboardActivity.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.dashboard.DashboardActivity", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DashboardAnalytics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.dashboard.DashboardAnalytics", "name": "DashboardAnalytics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.dashboard.DashboardAnalytics", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.dashboard", "mro": ["app.api.dashboard.DashboardAnalytics", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.dashboard.DashboardAnalytics.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.DashboardAnalytics.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.dashboard.DashboardAnalytics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.dashboard.DashboardAnalytics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DashboardSalesStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.dashboard.DashboardSalesStats", "name": "DashboardSalesStats", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.dashboard.DashboardSalesStats", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.dashboard", "mro": ["app.api.dashboard.DashboardSalesStats", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.dashboard.DashboardSalesStats.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.DashboardSalesStats.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.dashboard.DashboardSalesStats.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.dashboard.DashboardSalesStats", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DashboardStockStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.dashboard.DashboardStockStats", "name": "DashboardStockStats", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.dashboard.DashboardStockStats", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.dashboard", "mro": ["app.api.dashboard.DashboardStockStats", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.dashboard.DashboardStockStats.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.DashboardStockStats.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.dashboard.DashboardStockStats.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.dashboard.DashboardStockStats", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DashboardSummary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.dashboard.DashboardSummary", "name": "DashboardSummary", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.dashboard.DashboardSummary", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.dashboard", "mro": ["app.api.dashboard.DashboardSummary", "builtins.object"], "names": {".class": "SymbolTable", "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.dashboard.DashboardSummary.get", "name": "get", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.DashboardSummary.get", "name": "get", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.dashboard.DashboardSummary.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.dashboard.DashboardSummary", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Diamond": {".class": "SymbolTableNode", "cross_ref": "app.models.diamond.Diamond", "kind": "Gdef"}, "JewelryItem": {".class": "SymbolTableNode", "cross_ref": "app.models.jewelry.JewelryItem", "kind": "Gdef"}, "ManufacturingRequest": {".class": "SymbolTableNode", "cross_ref": "app.models.manufacturing.ManufacturingRequest", "kind": "Gdef"}, "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Resource", "source_any": null, "type_of_any": 3}}}, "Sale": {".class": "SymbolTableNode", "cross_ref": "app.models.sale.Sale", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.dashboard.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.dashboard.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.dashboard.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.dashboard.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.dashboard.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.dashboard.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "activity_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.dashboard.activity_model", "name": "activity_model", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "dashboard_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.dashboard.dashboard_ns", "name": "dashboard_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "db": {".class": "SymbolTableNode", "cross_ref": "app.db", "kind": "Gdef"}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.dashboard.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.dashboard.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.fields", "source_any": null, "type_of_any": 3}}}, "func": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.functions.func", "kind": "Gdef"}, "sales_stats_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.dashboard.sales_stats_model", "name": "sales_stats_model", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "stock_stats_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.dashboard.stock_stats_model", "name": "stock_stats_model", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "summary_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.dashboard.summary_model", "name": "summary_model", "type": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.dashboard.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "token_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.token_required", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\dashboard.py"}