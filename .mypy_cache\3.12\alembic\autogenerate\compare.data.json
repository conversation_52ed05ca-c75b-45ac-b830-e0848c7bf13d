{".class": "MypyFile", "_fullname": "alembic.autogenerate.compare", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AlterColumnOp": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.AlterColumnOp", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutogenContext": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.AutogenContext", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "Inspector": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.engine.reflection.Inspector", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef"}, "MigrationScript": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrationScript", "kind": "Gdef"}, "ModifyTableOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.ModifyTableOps", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedSet": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util._py_collections.OrderedSet", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "TextClause": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.TextClause", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "UpgradeOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.UpgradeOps", "kind": "Gdef"}, "_C": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.compare._C", "name": "_C", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.UniqueConstraint", "sqlalchemy.sql.schema.ForeignKeyConstraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "_IndexColumnSortingOps": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.compare._IndexColumnSortingOps", "name": "_IndexColumnSortingOps", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "_InspectorConv": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.compare._InspectorConv", "name": "_InspectorConv", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.compare", "mro": ["alembic.autogenerate.compare._InspectorConv", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "inspector"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv.__init__", "name": "__init__", "type": null}}, "__slots__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value", "allow_incompatible_override"], "fullname": "alembic.autogenerate.compare._InspectorConv.__slots__", "name": "__slots__", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_apply_constraint_conv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "consts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv._apply_constraint_conv", "name": "_apply_constraint_conv", "type": null}}, "_apply_reflectinfo_conv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "consts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv._apply_reflectinfo_conv", "name": "_apply_reflectinfo_conv", "type": null}}, "get_foreign_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv.get_foreign_keys", "name": "get_foreign_keys", "type": null}}, "get_indexes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv.get_indexes", "name": "get_indexes", "type": null}}, "get_unique_constraints": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["self", "args", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv.get_unique_constraints", "name": "get_unique_constraints", "type": null}}, "inspector": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.compare._InspectorConv.inspector", "name": "inspector", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "reflect_table": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "table", "include_columns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._InspectorConv.reflect_table", "name": "reflect_table", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.compare._InspectorConv.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.compare._InspectorConv", "values": [], "variance": 0}, "slots": ["inspector"], "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.compare.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_autogen_for_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["autogen_context", "upgrade_ops", "schemas"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._autogen_for_tables", "name": "_autogen_for_tables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["autogen_context", "upgrade_ops", "schemas"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.UpgradeOps", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autogen_for_tables", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._autogen_for_tables", "name": "_autogen_for_tables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["autogen_context", "upgrade_ops", "schemas"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.UpgradeOps", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.set"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_autogen_for_tables", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_column_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_column_comment", "name": "_compare_column_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.elements.quoted_name", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_column_comment", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_column_comment", "name": "_compare_column_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.elements.quoted_name", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_column_comment", "ret_type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.bool", "value": false}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["schema", "tname", "conn_table", "metadata_table", "modify_table_ops", "autogen_context", "inspector"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_columns", "name": "_compare_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["schema", "tname", "conn_table", "metadata_table", "modify_table_ops", "autogen_context", "inspector"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table", "alembic.operations.ops.ModifyTableOps", "alembic.autogenerate.api.AutogenContext", "sqlalchemy.engine.reflection.Inspector"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_columns", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_columns", "name": "_compare_columns", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["schema", "tname", "conn_table", "metadata_table", "modify_table_ops", "autogen_context", "inspector"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table", "alembic.operations.ops.ModifyTableOps", "alembic.autogenerate.api.AutogenContext", "sqlalchemy.engine.reflection.Inspector"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_columns", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_computed_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._compare_computed_default", "name": "_compare_computed_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_computed_default", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compare_foreign_keys": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_foreign_keys", "name": "_compare_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_foreign_keys", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_foreign_keys", "name": "_compare_foreign_keys", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.schema.Table", "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_foreign_keys", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_identity_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._compare_identity_default", "name": "_compare_identity_default", "type": null}}, "_compare_indexes_and_uniques": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_ops", "schema", "tname", "conn_table", "metadata_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_indexes_and_uniques", "name": "_compare_indexes_and_uniques", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_indexes_and_uniques", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_indexes_and_uniques", "name": "_compare_indexes_and_uniques", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_indexes_and_uniques", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_nullable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_nullable", "name": "_compare_nullable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_nullable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_nullable", "name": "_compare_nullable", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_nullable", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_server_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_server_default", "name": "_compare_server_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_server_default", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_server_default", "name": "_compare_server_default", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_server_default", "ret_type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_table_comment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_table_comment", "name": "_compare_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_table_comment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_table_comment", "name": "_compare_table_comment", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "modify_table_ops", "schema", "tname", "conn_table", "metadata_table"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.ModifyTableOps", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Table", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_table_comment", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_compare_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["conn_table_names", "metadata_table_names", "inspector", "upgrade_ops", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._compare_tables", "name": "_compare_tables", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["conn_table_names", "metadata_table_names", "inspector", "upgrade_ops", "autogen_context"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.set"}, "sqlalchemy.engine.reflection.Inspector", "alembic.operations.ops.UpgradeOps", "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_tables", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compare_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._compare_type", "name": "_compare_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_type", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._compare_type", "name": "_compare_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_type", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_constraint_sig": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl._autogen._constraint_sig", "kind": "Gdef"}, "_correct_for_uq_duplicates_uix": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0], "arg_names": ["conn_unique_constraints", "conn_indexes", "metadata_unique_constraints", "metadata_indexes", "dialect", "impl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._correct_for_uq_duplicates_uix", "name": "_correct_for_uq_duplicates_uix", "type": null}}, "_make_foreign_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["params", "conn_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._make_foreign_key", "name": "_make_foreign_key", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["params", "conn_table"], "arg_types": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_foreign_key", "ret_type": "sqlalchemy.sql.schema.ForeignKeyConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_index": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._make_index", "name": "_make_index", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "arg_types": ["alembic.ddl.impl.DefaultImpl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_index", "ret_type": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Index", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_unique_constraint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._make_unique_constraint", "name": "_make_unique_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["impl", "params", "conn_table"], "arg_types": ["alembic.ddl.impl.DefaultImpl", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "sqlalchemy.sql.schema.Table"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_unique_constraint", "ret_type": "sqlalchemy.sql.schema.UniqueConstraint", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_normalize_computed_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sqltext"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._normalize_computed_default", "name": "_normalize_computed_default", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sqltext"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_normalize_computed_default", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_populate_migration_script": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "migration_script"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._populate_migration_script", "name": "_populate_migration_script", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "migration_script"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_populate_migration_script", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_produce_net_changes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["autogen_context", "upgrade_ops"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._produce_net_changes", "name": "_produce_net_changes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["autogen_context", "upgrade_ops"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.UpgradeOps"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_produce_net_changes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_render_server_default_for_compare": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["metadata_default", "autogen_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._render_server_default_for_compare", "name": "_render_server_default_for_compare", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["metadata_default", "autogen_context"], "arg_types": [{".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_render_server_default_for_compare", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_autoincrement": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.compare._setup_autoincrement", "name": "_setup_autoincrement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.elements.quoted_name", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_autoincrement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "alembic.autogenerate.compare._setup_autoincrement", "name": "_setup_autoincrement", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0], "arg_names": ["autogen_context", "alter_column_op", "schema", "tname", "cname", "conn_col", "metadata_col"], "arg_types": ["alembic.autogenerate.api.AutogenContext", "alembic.operations.ops.AlterColumnOp", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["sqlalchemy.sql.elements.quoted_name", "builtins.str"], "uses_pep604_syntax": false}, "sqlalchemy.sql.elements.quoted_name", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "sqlalchemy.sql.schema.Column"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_setup_autoincrement", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_warn_computed_not_supported": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["tname", "cname"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.compare._warn_computed_not_supported", "name": "_warn_computed_not_supported", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["tname", "cname"], "arg_types": ["builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_warn_computed_not_supported", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "comparators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.compare.comparators", "name": "comparators", "type": "alembic.util.langhelpers.Dispatcher"}}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "conv": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.conv", "kind": "Gdef"}, "event": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.event", "kind": "Gdef"}, "expression": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.expression", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.inspection.inspect", "kind": "Gdef"}, "is_index_sig": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl._autogen.is_index_sig", "kind": "Gdef"}, "is_uq_sig": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl._autogen.is_uq_sig", "kind": "Gdef"}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.compare.log", "name": "log", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops", "kind": "Gdef"}, "quoted_name": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.elements.quoted_name", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "sa_schema": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.schema", "kind": "Gdef"}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}, "text": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._elements_constructors.text", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\autogenerate\\compare.py"}