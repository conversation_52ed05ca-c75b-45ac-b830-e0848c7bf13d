{".class": "MypyFile", "_fullname": "app.config", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.config.Config", "name": "Config", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.config.Config", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.config", "mro": ["app.config.Config", "builtins.object"], "names": {".class": "SymbolTable", "ACCOUNT_LOCKOUT_DURATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.ACCOUNT_LOCKOUT_DURATION", "name": "ACCOUNT_LOCKOUT_DURATION", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "JWT_ACCESS_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.JWT_ACCESS_TOKEN_EXPIRES", "name": "JWT_ACCESS_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "JWT_BLACKLIST_ENABLED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.JWT_BLACKLIST_ENABLED", "name": "JWT_BLACKLIST_ENABLED", "type": "builtins.bool"}}, "JWT_BLACKLIST_TOKEN_CHECKS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.JWT_BLACKLIST_TOKEN_CHECKS", "name": "JWT_BLACKLIST_TOKEN_CHECKS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "JWT_REFRESH_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.JWT_REFRESH_TOKEN_EXPIRES", "name": "JWT_REFRESH_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "JWT_SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.JWT_SECRET_KEY", "name": "JWT_SECRET_KEY", "type": "builtins.str"}}, "MAX_LOGIN_ATTEMPTS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.MAX_LOGIN_ATTEMPTS", "name": "MAX_LOGIN_ATTEMPTS", "type": "builtins.int"}}, "SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.SECRET_KEY", "name": "SECRET_KEY", "type": "builtins.str"}}, "SQLALCHEMY_TRACK_MODIFICATIONS": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.config.Config.SQLALCHEMY_TRACK_MODIFICATIONS", "name": "SQLALCHEMY_TRACK_MODIFICATIONS", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.config.Config.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.config.Config", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DevelopmentConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["app.config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.config.DevelopmentConfig", "name": "DevelopmentConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.config.DevelopmentConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.config", "mro": ["app.config.DevelopmentConfig", "app.config.Config", "builtins.object"], "names": {".class": "SymbolTable", "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.config.DevelopmentConfig.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "SQLALCHEMY_DATABASE_URI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.DevelopmentConfig.SQLALCHEMY_DATABASE_URI", "name": "SQLALCHEMY_DATABASE_URI", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.config.DevelopmentConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.config.DevelopmentConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProductionConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["app.config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.config.ProductionConfig", "name": "ProductionConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.config.ProductionConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.config", "mro": ["app.config.ProductionConfig", "app.config.Config", "builtins.object"], "names": {".class": "SymbolTable", "DEBUG": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.config.ProductionConfig.DEBUG", "name": "DEBUG", "type": "builtins.bool"}}, "JWT_ACCESS_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.ProductionConfig.JWT_ACCESS_TOKEN_EXPIRES", "name": "JWT_ACCESS_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "JWT_REFRESH_TOKEN_EXPIRES": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.ProductionConfig.JWT_REFRESH_TOKEN_EXPIRES", "name": "JWT_REFRESH_TOKEN_EXPIRES", "type": "datetime.<PERSON><PERSON><PERSON>"}}, "SQLALCHEMY_DATABASE_URI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.ProductionConfig.SQLALCHEMY_DATABASE_URI", "name": "SQLALCHEMY_DATABASE_URI", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.config.ProductionConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.config.ProductionConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestingConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["app.config.Config"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.config.TestingConfig", "name": "TestingConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "app.config.TestingConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.config", "mro": ["app.config.TestingConfig", "app.config.Config", "builtins.object"], "names": {".class": "SymbolTable", "JWT_SECRET_KEY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "app.config.TestingConfig.JWT_SECRET_KEY", "name": "JWT_SECRET_KEY", "type": "builtins.str"}}, "PRESERVE_CONTEXT_ON_EXCEPTION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.config.TestingConfig.PRESERVE_CONTEXT_ON_EXCEPTION", "name": "PRESERVE_CONTEXT_ON_EXCEPTION", "type": "builtins.bool"}}, "SQLALCHEMY_DATABASE_URI": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.config.TestingConfig.SQLALCHEMY_DATABASE_URI", "name": "SQLALCHEMY_DATABASE_URI", "type": "builtins.str"}}, "TESTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "app.config.TestingConfig.TESTING", "name": "TESTING", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.config.TestingConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.config.TestingConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.config.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.config.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.config.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.config.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.config.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.config.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.config.config", "name": "config", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["app.config.DevelopmentConfig"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "app.config.Config", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "load_dotenv": {".class": "SymbolTableNode", "cross_ref": "dotenv.main.load_dotenv", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "E:\\admin_panel\\admin_backend\\app\\config.py"}