import React from 'react';
import Input from '../../components/ui/Input';
import Button from '../../components/ui/Button';
import Card from '../../components/ui/Card';
import { useManagementState } from '../../hooks/useManagementState';
import toast from 'react-hot-toast';

const ManageShapes: React.FC = () => {
  const {
    items: shapes,
    isLoading,
    error,
    newItem: newShape,
    setNewItem: setNewShape,
    addItemMutation: addShapeMutation,
    deleteItemMutation: deleteShapeMutation,
  } = useManagementState({    queryKey: 'shapes',
    fetchUrl: '/shapes',
    createUrl: '/shapes',
    deleteUrl: '/shapes',
  });

  const handleAddShape = () => {
    if (!newShape.trim()) {
      toast.error('Shape name cannot be empty');
      return;
    }
    if (newShape.length > 50) {
      toast.error('Shape name must be less than 50 characters');
      return;
    }
    addShapeMutation.mutate(newShape);
  };

  const handleDeleteShape = (shapeId: number) => {
    if (window.confirm('Are you sure you want to delete this shape?')) {
      deleteShapeMutation.mutate(shapeId);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">Manage Shapes</h1>
      <Card>
        <div className="space-y-4">
          <Input
            label="New Shape"
            value={newShape}
            onChange={(e) => setNewShape(e.target.value)}
            placeholder="Enter shape name"
          />
          <Button onClick={handleAddShape} isLoading={addShapeMutation.isPending}>
            Add Shape
          </Button>
        </div>
      </Card>
      <div className="space-y-4">
        {isLoading && <p>Loading shapes...</p>}
        {error && <p>Error loading shapes</p>}
        {shapes?.map((shape: { id: number; name: string }) => (
          <div key={shape.id} className="flex justify-between items-center">
            <span>{shape.name}</span>
            <Button onClick={() => handleDeleteShape(shape.id)} isLoading={deleteShapeMutation.isPending}>
              Delete
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ManageShapes;
