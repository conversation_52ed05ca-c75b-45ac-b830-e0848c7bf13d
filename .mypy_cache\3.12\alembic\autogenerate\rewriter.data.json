{".class": "MypyFile", "_fullname": "alembic.autogenerate.rewriter", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AddColumnOp": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.AddColumnOp", "kind": "Gdef"}, "AlterColumnOp": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.AlterColumnOp", "kind": "Gdef"}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CreateTableOp": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.CreateTableOp", "kind": "Gdef"}, "DowngradeOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.DowngradeOps", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MigrateOperation": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrateOperation", "kind": "Gdef"}, "MigrationContext": {".class": "SymbolTableNode", "cross_ref": "alembic.runtime.migration.MigrationContext", "kind": "Gdef"}, "MigrationScript": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.MigrationScript", "kind": "Gdef"}, "ModifyTableOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.ModifyTableOps", "kind": "Gdef"}, "OpContainer": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.OpContainer", "kind": "Gdef"}, "ProcessRevisionDirectiveFn": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.autogenerate.rewriter.ProcessRevisionDirectiveFn", "line": 28, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, {".class": "Instance", "args": ["alembic.operations.ops.MigrationScript"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "Rewriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.autogenerate.rewriter.Rewriter", "name": "Rewriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.autogenerate.rewriter", "mro": ["alembic.autogenerate.rewriter.Rewriter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directives"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directives"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, {".class": "Instance", "args": ["alembic.operations.ops.MigrationScript"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Re<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_chained": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "alembic.autogenerate.rewriter.Rewriter._chained", "name": "_chained", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.autogenerate.rewriter.ProcessRevisionDirectiveFn"}, "alembic.autogenerate.rewriter.Rewriter"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "_rewrite": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator"], "fullname": "alembic.autogenerate.rewriter.Rewriter._rewrite", "name": "_rewrite", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_rewrite of Re<PERSON>", "ret_type": {".class": "Instance", "args": ["alembic.operations.ops.MigrateOperation"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_traverse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse", "name": "_traverse", "type": "alembic.util.langhelpers.Dispatcher"}}, "_traverse_any_directive": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_any_directive", "name": "_traverse_any_directive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_any_directive of Re<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_any_directive", "name": "_traverse_any_directive", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_any_directive of Re<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_traverse_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_for", "name": "_traverse_for", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.MigrateOperation"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_for of Rewriter", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_traverse_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directives"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_list", "name": "_traverse_list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directives"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_list of Rewriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_traverse_op_container": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_op_container", "name": "_traverse_op_container", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.OpContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_op_container of Rewriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_op_container", "name": "_traverse_op_container", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.OpContainer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_op_container of Rewriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_traverse_script": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_script", "name": "_traverse_script", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_script of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.autogenerate.rewriter.Rewriter._traverse_script", "name": "_traverse_script", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directive"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, "alembic.operations.ops.MigrationScript"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_traverse_script of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chain": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter.chain", "name": "chain", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.autogenerate.rewriter.ProcessRevisionDirectiveFn"}, "alembic.autogenerate.rewriter.Rewriter"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "chain of Rewriter", "ret_type": "alembic.autogenerate.rewriter.Rewriter", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dispatch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.autogenerate.rewriter.Rewriter.dispatch", "name": "dispatch", "type": "alembic.util.langhelpers.Dispatcher"}}, "process_revision_directives": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directives"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter.process_revision_directives", "name": "process_revision_directives", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "context", "revision", "directives"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", "alembic.runtime.migration.MigrationContext", {".class": "TypeAliasType", "args": [], "type_ref": "alembic.script.revision._GetRevArg"}, {".class": "Instance", "args": ["alembic.operations.ops.MigrationScript"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_revision_directives of Rewriter", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "rewrites": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "operator"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.autogenerate.rewriter.Rewriter.rewrites", "name": "rewrites", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "operator"], "arg_types": ["alembic.autogenerate.rewriter.Rewriter", {".class": "UnionType", "items": [{".class": "TypeType", "item": "alembic.operations.ops.AddColumnOp"}, {".class": "TypeType", "item": "alembic.operations.ops.MigrateOperation"}, {".class": "TypeType", "item": "alembic.operations.ops.AlterColumnOp"}, {".class": "TypeType", "item": "alembic.operations.ops.CreateTableOp"}, {".class": "TypeType", "item": "alembic.operations.ops.ModifyTableOps"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "rewrites of <PERSON><PERSON>", "ret_type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.autogenerate.rewriter.Rewriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.autogenerate.rewriter.Rewriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UpgradeOps": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops.UpgradeOps", "kind": "Gdef"}, "_GetRevArg": {".class": "SymbolTableNode", "cross_ref": "alembic.script.revision._GetRevArg", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.rewriter.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.rewriter.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.rewriter.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.rewriter.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.rewriter.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.autogenerate.rewriter.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "ops": {".class": "SymbolTableNode", "cross_ref": "alembic.operations.ops", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\autogenerate\\rewriter.py"}