{"data_mtime": 1753088015, "dep_lines": [21, 27, 28, 596, 17, 19, 20, 22, 23, 27, 1, 1, 421], "dep_prios": [5, 25, 25, 20, 5, 10, 10, 5, 5, 20, 5, 30, 20], "dependencies": ["collections.abc", "PIL._imaging", "PIL._typing", "PIL.Image", "__future__", "abc", "functools", "types", "typing", "PIL", "builtins", "_frozen_importlib"], "hash": "c45ecc7dbfa786ed03326864879d883f2d2801d9", "id": "PIL.ImageFilter", "ignore_all": true, "interface_hash": "8cc6111183385ccefd9b5433b1dd15aaeed6fa9a", "mtime": 1750655007, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\PIL\\ImageFilter.py", "plugin_data": null, "size": 19275, "suppressed": ["numpy"], "version_id": "1.15.0"}