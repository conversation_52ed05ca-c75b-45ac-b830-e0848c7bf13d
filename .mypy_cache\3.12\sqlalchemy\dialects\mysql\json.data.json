{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql.json", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "JSON": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.sql.sqltypes.JSON"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.json.JSON", "name": "JSON", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json.JSON", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.json", "mro": ["sqlalchemy.dialects.mysql.json.JSON", "sqlalchemy.sql.sqltypes.JSON", "sqlalchemy.sql.sqltypes.Indexable", "sqlalchemy.sql.type_api.TypeEngineMixin", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.json.JSON.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.json.JSON", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSONIndexType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.json._FormatTypeMixin", "sqlalchemy.sql.sqltypes.JSON.JSONIndexType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.json.JSONIndexType", "name": "JSONIndexType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json.JSONIndexType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.json", "mro": ["sqlalchemy.dialects.mysql.json.JSONIndexType", "sqlalchemy.dialects.mysql.json._FormatTypeMixin", "sqlalchemy.sql.sqltypes.JSON.JSONIndexType", "sqlalchemy.sql.sqltypes.JSON.JSONElementType", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "_format_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json.JSONIndexType._format_value", "name": "_format_value", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.json.JSONIndexType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.json.JSONIndexType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "JSONPathType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.json._FormatTypeMixin", "sqlalchemy.sql.sqltypes.JSON.JSONPathType"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.json.JSONPathType", "name": "JSONPathType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json.JSONPathType", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.json", "mro": ["sqlalchemy.dialects.mysql.json.JSONPathType", "sqlalchemy.dialects.mysql.json._FormatTypeMixin", "sqlalchemy.sql.sqltypes.JSON.JSONPathType", "sqlalchemy.sql.sqltypes.JSON.JSONElementType", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "_format_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json.JSONPathType._format_value", "name": "_format_value", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.json.JSONPathType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.json.JSONPathType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_FormatTypeMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.json._FormatTypeMixin", "name": "_FormatTypeMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json._FormatTypeMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.json", "mro": ["sqlalchemy.dialects.mysql.json._FormatTypeMixin", "builtins.object"], "names": {".class": "SymbolTable", "_format_value": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json._FormatTypeMixin._format_value", "name": "_format_value", "type": null}}, "bind_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json._FormatTypeMixin.bind_processor", "name": "bind_processor", "type": null}}, "literal_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dialect"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.json._FormatTypeMixin.literal_processor", "name": "literal_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.json._FormatTypeMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.json._FormatTypeMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.json.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.json.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.json.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.json.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.json.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.json.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "sqltypes": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.types", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\json.py"}