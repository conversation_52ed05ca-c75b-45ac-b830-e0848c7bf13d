{"data_mtime": 1753035429, "dep_lines": [28, 37, 38, 40, 43, 36, 37, 39, 8, 10, 11, 14, 15, 36, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 25, 10, 20, 5, 5, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["sqlalchemy.dialects.postgresql.operators", "sqlalchemy.sql.operators", "sqlalchemy.sql.type_api", "sqlalchemy.util.typing", "sqlalchemy.sql.elements", "sqlalchemy.types", "sqlalchemy.sql", "sqlalchemy.util", "__future__", "dataclasses", "datetime", "decimal", "typing", "sqlalchemy", "builtins", "_frozen_importlib", "abc", "enum", "sqlalchemy.sql.annotation", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util.compat", "sqlalchemy.util.langhelpers", "types"], "hash": "7b104ea37794f7497c77af828186aa2128fef4cc", "id": "sqlalchemy.dialects.postgresql.ranges", "ignore_all": true, "interface_hash": "407b800c28f9f8c2fd464f7f66dd9cfdb3bd2f4f", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\postgresql\\ranges.py", "plugin_data": null, "size": 34009, "suppressed": [], "version_id": "1.15.0"}