{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.mysql.mysqlconnector", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BIT": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.types.BIT", "kind": "Gdef"}, "IdentifierPreparerCommon_mysqlconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "name": "IdentifierPreparerCommon_mysqlconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "builtins.object"], "names": {".class": "SymbolTable", "_double_percents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": ["is_property"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector._double_percents", "impl": null, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_overload", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector._double_percents", "name": "_double_percents", "type": null}, "is_overload": true, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_settable_property", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector._double_percents", "name": "_double_percents", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_double_percents of IdentifierPreparerCommon_mysqlconnector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector._double_percents", "name": "_double_percents", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_inferred"], "fullname": "", "name": "_double_percents", "type": null}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_double_percents", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "_escape_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector._escape_identifier", "name": "_escape_identifier", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MariaDBDialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.mariadb.MariaDBDialect", "kind": "Gdef"}, "MariaDBDialect_mysqlconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.mariadb.MariaDBDialect", "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector", "name": "MariaDBDialect_mysqlconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector", "sqlalchemy.dialects.mysql.mariadb.MariaDBDialect", "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector", "sqlalchemy.dialects.mysql.base.MySQLDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "_allows_uuid_binds": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector._allows_uuid_binds", "name": "_allows_uuid_binds", "type": "builtins.bool"}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector.preparer", "name": "preparer", "type": null}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MariaDBIdentifierPreparer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer", "kind": "Gdef"}, "MariaDBIdentifierPreparer_mysqlconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBIdentifierPreparer_mysqlconnector", "name": "MariaDBIdentifierPreparer_mysqlconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBIdentifierPreparer_mysqlconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector.MariaDBIdentifierPreparer_mysqlconnector", "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "sqlalchemy.dialects.mysql.base.MariaDBIdentifierPreparer", "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBIdentifierPreparer_mysqlconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBIdentifierPreparer_mysqlconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLCompiler": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MySQLCompiler", "kind": "Gdef"}, "MySQLCompiler_mysqlconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.base.MySQLCompiler"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector", "name": "MySQLCompiler_mysqlconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector", "sqlalchemy.dialects.mysql.base.MySQLCompiler", "sqlalchemy.sql.compiler.SQLCompiler", "sqlalchemy.sql.compiler.Compiled", "builtins.object"], "names": {".class": "SymbolTable", "visit_mod_binary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 4], "arg_names": ["self", "binary", "operator", "kw"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector.visit_mod_binary", "name": "visit_mod_binary", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLDialect": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MySQLDialect", "kind": "Gdef"}, "MySQLDialect_mysqlconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.base.MySQLDialect"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector", "name": "MySQLDialect_mysqlconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector", "sqlalchemy.dialects.mysql.base.MySQLDialect", "sqlalchemy.engine.default.DefaultDialect", "sqlalchemy.engine.interfaces.Dialect", "sqlalchemy.event.registry.EventTarget", "builtins.object"], "names": {".class": "SymbolTable", "_compat_fetchall": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rp", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector._compat_fetchall", "name": "_compat_fetchall", "type": null}}, "_compat_fetchone": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "rp", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector._compat_fetchone", "name": "_compat_fetchone", "type": null}}, "_detect_charset": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector._detect_charset", "name": "_detect_charset", "type": null}}, "_extract_error_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector._extract_error_code", "name": "_extract_error_code", "type": null}}, "_mysqlconnector_version_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector._mysqlconnector_version_info", "name": "_mysqlconnector_version_info", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector._mysqlconnector_version_info", "name": "_mysqlconnector_version_info", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "colspecs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.colspecs", "name": "colspecs", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "create_connect_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "url"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.create_connect_args", "name": "create_connect_args", "type": null}}, "default_paramstyle": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.default_paramstyle", "name": "default_paramstyle", "type": "builtins.str"}}, "do_ping": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.do_ping", "name": "do_ping", "type": null}}, "driver": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.driver", "name": "driver", "type": "builtins.str"}}, "execution_ctx_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.execution_ctx_cls", "name": "execution_ctx_cls", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_isolation_level_values": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "dbapi_connection"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.get_isolation_level_values", "name": "get_isolation_level_values", "type": null}}, "import_dbapi": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.import_dbapi", "name": "import_dbapi", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.import_dbapi", "name": "import_dbapi", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "import_dbapi of MySQLDialect_mysqlconnector", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_disconnect": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "e", "connection", "cursor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.is_disconnect", "name": "is_disconnect", "type": null}}, "preparer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.preparer", "name": "preparer", "type": null}}, "set_isolation_level": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "connection", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.set_isolation_level", "name": "set_isolation_level", "type": null}}, "statement_compiler": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.statement_compiler", "name": "statement_compiler", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["dialect", "statement", "cache_key", "column_keys", "for_executemany", "linting", "_supporting_against", "kwargs"], "arg_types": ["sqlalchemy.engine.interfaces.Dialect", {".class": "UnionType", "items": ["sqlalchemy.sql.elements.ClauseElement", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "sqlalchemy.sql.cache_key.CacheKey"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "sqlalchemy.sql.compiler.<PERSON>", {".class": "UnionType", "items": ["sqlalchemy.sql.compiler.SQLCompiler", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": ["sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLCompiler_mysqlconnector", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "supports_native_bit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.supports_native_bit", "name": "supports_native_bit", "type": "builtins.bool"}}, "supports_native_decimal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.supports_native_decimal", "name": "supports_native_decimal", "type": "builtins.bool"}}, "supports_sane_multi_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.supports_sane_multi_rowcount", "name": "supports_sane_multi_rowcount", "type": "builtins.bool"}}, "supports_sane_rowcount": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.supports_sane_rowcount", "name": "supports_sane_rowcount", "type": "builtins.bool"}}, "supports_server_side_cursors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.supports_server_side_cursors", "name": "supports_server_side_cursors", "type": "builtins.bool"}}, "supports_statement_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.supports_statement_cache", "name": "supports_statement_cache", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLExecutionContext": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "kind": "Gdef"}, "MySQLExecutionContext_mysqlconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.base.MySQLExecutionContext"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector", "name": "MySQLExecutionContext_mysqlconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector", "sqlalchemy.dialects.mysql.base.MySQLExecutionContext", "sqlalchemy.engine.default.DefaultExecutionContext", "sqlalchemy.engine.interfaces.ExecutionContext", "builtins.object"], "names": {".class": "SymbolTable", "create_default_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector.create_default_cursor", "name": "create_default_cursor", "type": null}}, "create_server_side_cursor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector.create_server_side_cursor", "name": "create_server_side_cursor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLExecutionContext_mysqlconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MySQLIdentifierPreparer": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "kind": "Gdef"}, "MySQLIdentifierPreparer_mysqlconnector": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLIdentifierPreparer_mysqlconnector", "name": "MySQLIdentifierPreparer_mysqlconnector", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLIdentifierPreparer_mysqlconnector", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector.MySQLIdentifierPreparer_mysqlconnector", "sqlalchemy.dialects.mysql.mysqlconnector.IdentifierPreparerCommon_mysqlconnector", "sqlalchemy.dialects.mysql.base.MySQLIdentifierPreparer", "sqlalchemy.sql.compiler.IdentifierPreparer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLIdentifierPreparer_mysqlconnector.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLIdentifierPreparer_mysqlconnector", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_myconnpyBIT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["sqlalchemy.dialects.mysql.types.BIT"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "sqlalchemy.dialects.mysql.mysqlconnector._myconnpyBIT", "name": "_myconnpyBIT", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector._myconnpyBIT", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "sqlalchemy.dialects.mysql.mysqlconnector", "mro": ["sqlalchemy.dialects.mysql.mysqlconnector._myconnpyBIT", "sqlalchemy.dialects.mysql.types.BIT", "sqlalchemy.sql.type_api.TypeEngine", "sqlalchemy.sql.visitors.Visitable", "builtins.object"], "names": {".class": "SymbolTable", "result_processor": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "dialect", "coltype"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "sqlalchemy.dialects.mysql.mysqlconnector._myconnpyBIT.result_processor", "name": "result_processor", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector._myconnpyBIT.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "sqlalchemy.dialects.mysql.mysqlconnector._myconnpyBIT", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.dialect", "line": 252, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.mysqlconnector.MySQLDialect_mysqlconnector"}}, "mariadb_dialect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "sqlalchemy.dialects.mysql.mysqlconnector.mariadb_dialect", "line": 253, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "sqlalchemy.dialects.mysql.mysqlconnector.MariaDBDialect_mysqlconnector"}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\mysql\\mysqlconnector.py"}