{"data_mtime": 1753035443, "dep_lines": [26, 27, 30, 36, 40, 52, 53, 55, 56, 57, 24, 26, 29, 35, 1, 3, 4, 5, 6, 7, 29, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 10, 25, 25, 25, 25, 25, 25, 25, 5, 20, 10, 25, 5, 5, 10, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["alembic.operations.schemaobj", "alembic.operations.base", "alembic.util.sqla_compat", "sqlalchemy.sql.elements", "sqlalchemy.sql.schema", "sqlalchemy.sql.selectable", "sqlalchemy.sql.type_api", "alembic.autogenerate.rewriter", "alembic.runtime.migration", "alembic.script.revision", "sqlalchemy.types", "alembic.operations", "alembic.util", "sqlalchemy.sql", "__future__", "abc", "os", "pathlib", "re", "typing", "alembic", "builtins", "_frozen_importlib", "alembic.autogenerate", "alembic.operations.batch", "alembic.runtime", "alembic.util.langhelpers", "enum", "sqlalchemy", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.inspection", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.operators", "sqlalchemy.sql.roles", "sqlalchemy.sql.traversals", "sqlalchemy.sql.visitors", "sqlalchemy.util", "sqlalchemy.util.langhelpers", "types"], "hash": "304a1c526500ee90b5ec19db45b554931c45606c", "id": "alembic.operations.ops", "ignore_all": true, "interface_hash": "b20b7a22b161b55ad9f6cb914c32b4fdfb76cc99", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\operations\\ops.py", "plugin_data": null, "size": 96276, "suppressed": [], "version_id": "1.15.0"}