{"data_mtime": 1753074106, "dep_lines": [3, 6, 7, 2, 4, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 8, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["app.models.user", "app.utils.decorators", "app.utils.error_handler", "flask_jwt_extended", "app", "marshmallow", "builtins", "_frozen_importlib", "abc", "app.models", "app.utils", "flask_jwt_extended.view_decorators", "flask_sqlalchemy", "flask_sqlalchemy.extension", "marshmallow.base", "marshmallow.exceptions", "marshmallow.schema", "typing"], "hash": "b263c6a06de0354c22191a8e0406ba74eb6a8b80", "id": "app.api.users", "ignore_all": true, "interface_hash": "121accadf4428d61f282d4c2fa9db30510d47660", "mtime": 1750925353, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\users.py", "plugin_data": null, "size": 4886, "suppressed": ["passlib.hash", "flask_restx"], "version_id": "1.15.0"}