{"data_mtime": 1753035436, "dep_lines": [498, 502, 498, 505, 506, 507, 508, 509, 503, 504, 505, 508, 492, 494, 495, 496, 503, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1168], "dep_prios": [5, 5, 20, 10, 10, 10, 10, 5, 10, 10, 20, 20, 5, 10, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 20], "dependencies": ["sqlalchemy.dialects.oracle.base", "sqlalchemy.dialects.oracle.types", "sqlalchemy.dialects.oracle", "sqlalchemy.engine.cursor", "sqlalchemy.engine.interfaces", "sqlalchemy.engine.processors", "sqlalchemy.sql.sqltypes", "sqlalchemy.sql._typing", "sqlalchemy.exc", "sqlalchemy.util", "sqlalchemy.engine", "sqlalchemy.sql", "__future__", "decimal", "random", "re", "sqlalchemy", "builtins", "_collections_abc", "_frozen_importlib", "_typeshed", "abc", "enum", "sqlalchemy.engine.default", "sqlalchemy.event", "sqlalchemy.event.registry", "sqlalchemy.sql.annotation", "sqlalchemy.sql.base", "sqlalchemy.sql.cache_key", "sqlalchemy.sql.compiler", "sqlalchemy.sql.elements", "sqlalchemy.sql.traversals", "sqlalchemy.sql.type_api", "sqlalchemy.sql.visitors", "sqlalchemy.util._py_collections", "sqlalchemy.util.langhelpers", "typing", "uuid"], "hash": "3519566901be9576a707b68e5dd3745a62d424a9", "id": "sqlalchemy.dialects.oracle.cx_oracle", "ignore_all": true, "interface_hash": "3d36a2c03ff17b1ad8f1ab4677e10c4aa38ebfee", "mtime": 1750655027, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\cx_oracle.py", "plugin_data": null, "size": 58164, "suppressed": ["cx_Oracle"], "version_id": "1.15.0"}