{".class": "MypyFile", "_fullname": "alembic.ddl._autogen", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AutogenContext": {".class": "SymbolTableNode", "cross_ref": "alembic.autogenerate.api.AutogenContext", "kind": "Gdef"}, "ClassVar": {".class": "SymbolTableNode", "cross_ref": "typing.ClassVar", "kind": "Gdef"}, "CompareConstraintType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "alembic.ddl._autogen.CompareConstraintType", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}}}, "ComparisonResult": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl._autogen.ComparisonResult", "name": "ComparisonResult", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "alembic.ddl._autogen.ComparisonResult", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["status", "message"]}}, "module_name": "alembic.ddl._autogen", "mro": ["alembic.ddl._autogen.ComparisonResult", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "Different": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen.ComparisonResult.Different", "name": "Different", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "reason"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Different of ComparisonResult", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen.ComparisonResult.Different", "name": "Different", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "reason"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Different of ComparisonResult", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "Equal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen.ComparisonResult.Equal", "name": "Equal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Equal of ComparisonResult", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen.ComparisonResult.Equal", "name": "Equal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "Equal of ComparisonResult", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "Skip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "reason"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen.ComparisonResult.Skip", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "reason"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON> of ComparisonResult", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen.ComparisonResult.Skip", "name": "<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "reason"], "arg_types": [{".class": "TypeType", "item": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "<PERSON><PERSON> of ComparisonResult", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "status"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "message"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "status", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "alembic.ddl._autogen.ComparisonResult.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "status", "message"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of ComparisonResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen.ComparisonResult._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of ComparisonResult", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen.ComparisonResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ComparisonResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of ComparisonResult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "status", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen.ComparisonResult._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "status", "message"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of ComparisonR<PERSON>ult", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult._NT", "id": -1, "name": "_NT", "namespace": "alembic.ddl._autogen.ComparisonResult._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult._source", "name": "_source", "type": "builtins.str"}}, "is_different": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.ddl._autogen.ComparisonResult.is_different", "name": "is_different", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_different of ComparisonResult", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen.ComparisonResult.is_different", "name": "is_different", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_different of ComparisonResult", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_equal": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.ddl._autogen.ComparisonResult.is_equal", "name": "is_equal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_equal of ComparisonResult", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen.ComparisonResult.is_equal", "name": "is_equal", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_equal of ComparisonResult", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_skip": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.ddl._autogen.ComparisonResult.is_skip", "name": "is_skip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_skip of ComparisonResult", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen.ComparisonResult.is_skip", "name": "is_skip", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_skip of ComparisonResult", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult.message", "name": "message", "type": "builtins.str"}}, "message-redefinition": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl._autogen.ComparisonResult.message", "kind": "<PERSON><PERSON><PERSON>"}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "alembic.ddl._autogen.ComparisonResult.status", "name": "status", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}}}, "status-redefinition": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl._autogen.ComparisonResult.status", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen.ComparisonResult.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": "alembic.ddl._autogen.ComparisonResult"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "equal"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "different"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "skip"}], "uses_pep604_syntax": false}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "Constraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Constraint", "kind": "Gdef"}, "DefaultImpl": {".class": "SymbolTableNode", "cross_ref": "alembic.ddl.impl.DefaultImpl", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "ForeignKeyConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.ForeignKeyConstraint", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Index": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Index", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NamedTuple": {".class": "SymbolTableNode", "cross_ref": "typing.NamedTuple", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "typing.Type", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UniqueConstraint": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.UniqueConstraint", "kind": "Gdef"}, "_C": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "name": "_C", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl._autogen.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl._autogen.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl._autogen.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl._autogen.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl._autogen.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "alembic.ddl._autogen.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_clsreg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "alembic.ddl._autogen._clsreg", "name": "_clsreg", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl._autogen._constraint_sig", "name": "_constraint_sig", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl._autogen", "mro": ["alembic.ddl._autogen._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__eq__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig.__eq__", "name": "__eq__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__eq__ of _constraint_sig", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig.__hash__", "name": "__hash__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__hash__ of _constraint_sig", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, "builtins.bool", "alembic.ddl.impl.DefaultImpl", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init_subclass__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class"], "fullname": "alembic.ddl._autogen._constraint_sig.__init_subclass__", "name": "__init_subclass__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init_subclass__ of _constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__ne__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig.__ne__", "name": "__ne__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__ne__ of _constraint_sig", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compare_to_reflected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig._compare_to_reflected", "name": "_compare_to_reflected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_to_reflected of _constraint_sig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_full_sig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._constraint_sig._full_sig", "name": "_full_sig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_full_sig of _constraint_sig", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._constraint_sig._full_sig", "name": "_full_sig", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "_is_fk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "alembic.ddl._autogen._constraint_sig._is_fk", "name": "_is_fk", "type": "builtins.bool"}}, "_is_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "alembic.ddl._autogen._constraint_sig._is_index", "name": "_is_index", "type": "builtins.bool"}}, "_is_metadata": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen._constraint_sig._is_metadata", "name": "_is_metadata", "type": "builtins.bool"}}, "_is_uq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_ready", "has_explicit_value"], "fullname": "alembic.ddl._autogen._constraint_sig._is_uq", "name": "_is_uq", "type": "builtins.bool"}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen._constraint_sig._register", "name": "_register", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._constraint_sig._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _constraint_sig", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_sig": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen._constraint_sig._sig", "name": "_sig", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "compare_to_reflected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig.compare_to_reflected", "name": "compare_to_reflected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "compare_to_reflected of _constraint_sig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen._constraint_sig.const", "name": "const", "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}}}, "from_constraint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "is_metadata", "impl", "constraint"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen._constraint_sig.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "is_metadata", "impl", "constraint"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}}, "builtins.bool", "alembic.ddl.impl.DefaultImpl", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of _constraint_sig", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._constraint_sig.from_constraint", "name": "from_constraint", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["cls", "is_metadata", "impl", "constraint"], "arg_types": [{".class": "TypeType", "item": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}}, "builtins.bool", "alembic.ddl.impl.DefaultImpl", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "from_constraint of _constraint_sig", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen._constraint_sig.impl", "name": "impl", "type": "alembic.ddl.impl.DefaultImpl"}}, "is_named": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._constraint_sig.is_named", "name": "is_named", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._constraint_sig.is_named", "name": "is_named", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "md_name_to_sql_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._constraint_sig.md_name_to_sql_name", "name": "md_name_to_sql_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, "alembic.autogenerate.api.AutogenContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "md_name_to_sql_name of _constraint_sig", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen._constraint_sig.name", "name": "name", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintNameDefined"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "unnamed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._constraint_sig.unnamed", "name": "unnamed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unnamed of _constraint_sig", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._constraint_sig.unnamed", "name": "unnamed", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "unnamed_no_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._constraint_sig.unnamed_no_options", "name": "unnamed_no_options", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unnamed_no_options of _constraint_sig", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._constraint_sig.unnamed_no_options", "name": "unnamed_no_options", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": 1, "name": "_C", "namespace": "alembic.ddl._autogen._constraint_sig", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["_C"], "typeddict_type": null}}, "_fk_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.ForeignKeyConstraint"], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl._autogen._fk_constraint_sig", "name": "_fk_constraint_sig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._fk_constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl._autogen", "mro": ["alembic.ddl._autogen._fk_constraint_sig", "alembic.ddl._autogen._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._fk_constraint_sig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "arg_types": ["alembic.ddl._autogen._fk_constraint_sig", "builtins.bool", "alembic.ddl.impl.DefaultImpl", "sqlalchemy.sql.schema.ForeignKeyConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _fk_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_fk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl._autogen._fk_constraint_sig._is_fk", "name": "_is_fk", "type": "builtins.bool"}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen._fk_constraint_sig._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl._autogen._fk_constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _fk_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl._autogen._fk_constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _fk_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_sig": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig._sig", "name": "_sig", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "source_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.source_columns", "name": "source_columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "source_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.source_schema", "name": "source_schema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "source_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.source_table", "name": "source_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "target_columns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.target_columns", "name": "target_columns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "target_schema": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.target_schema", "name": "target_schema", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "target_table": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.target_table", "name": "target_table", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_of_any": 7}}}, "unnamed_no_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.unnamed_no_options", "name": "unnamed_no_options", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._fk_constraint_sig.unnamed_no_options", "name": "unnamed_no_options", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._fk_constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl._autogen._fk_constraint_sig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ix_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.Index"], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl._autogen._ix_constraint_sig", "name": "_ix_constraint_sig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._ix_constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl._autogen", "mro": ["alembic.ddl._autogen._ix_constraint_sig", "alembic.ddl._autogen._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._ix_constraint_sig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "arg_types": ["alembic.ddl._autogen._ix_constraint_sig", "builtins.bool", "alembic.ddl.impl.DefaultImpl", "sqlalchemy.sql.schema.Index"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _ix_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compare_to_reflected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._ix_constraint_sig._compare_to_reflected", "name": "_compare_to_reflected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["alembic.ddl._autogen._ix_constraint_sig", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": -1, "name": "_C", "namespace": "alembic.ddl._autogen._ix_constraint_sig._compare_to_reflected", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_to_reflected of _ix_constraint_sig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": -1, "name": "_C", "namespace": "alembic.ddl._autogen._ix_constraint_sig._compare_to_reflected", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "_is_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl._autogen._ix_constraint_sig._is_index", "name": "_is_index", "type": "builtins.bool"}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen._ix_constraint_sig._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl._autogen._ix_constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _ix_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._ix_constraint_sig._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl._autogen._ix_constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _ix_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "column_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.column_names", "name": "column_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.ddl._autogen._ix_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names of _ix_constraint_sig", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.column_names", "name": "column_names", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "column_names_optional": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.column_names_optional", "name": "column_names_optional", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.ddl._autogen._ix_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names_optional of _ix_constraint_sig", "ret_type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.column_names_optional", "name": "column_names_optional", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "has_expressions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.has_expressions", "name": "has_expressions", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.has_expressions", "name": "has_expressions", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "is_named": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.is_named", "name": "is_named", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.is_named", "name": "is_named", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}, "is_unique": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.is_unique", "name": "is_unique", "type": "builtins.bool"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.name", "name": "name", "type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.util.sqla_compat._ConstraintName"}}}, "unnamed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.unnamed", "name": "unnamed", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._ix_constraint_sig.unnamed", "name": "unnamed", "type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}], "extra_attrs": null, "type_ref": "sqlalchemy.util.langhelpers.generic_fn_descriptor"}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._ix_constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl._autogen._ix_constraint_sig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_uq_constraint_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["sqlalchemy.sql.schema.UniqueConstraint"], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "alembic.ddl._autogen._uq_constraint_sig", "name": "_uq_constraint_sig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._uq_constraint_sig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "alembic.ddl._autogen", "mro": ["alembic.ddl._autogen._uq_constraint_sig", "alembic.ddl._autogen._constraint_sig", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._uq_constraint_sig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "is_metadata", "impl", "const"], "arg_types": ["alembic.ddl._autogen._uq_constraint_sig", "builtins.bool", "alembic.ddl.impl.DefaultImpl", "sqlalchemy.sql.schema.UniqueConstraint"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _uq_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_compare_to_reflected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "other"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen._uq_constraint_sig._compare_to_reflected", "name": "_compare_to_reflected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "other"], "arg_types": ["alembic.ddl._autogen._uq_constraint_sig", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": -1, "name": "_C", "namespace": "alembic.ddl._autogen._uq_constraint_sig._compare_to_reflected", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_compare_to_reflected of _uq_constraint_sig", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "alembic.ddl._autogen.ComparisonResult"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._C", "id": -1, "name": "_C", "namespace": "alembic.ddl._autogen._uq_constraint_sig._compare_to_reflected", "upper_bound": {".class": "UnionType", "items": ["sqlalchemy.sql.schema.Constraint", "sqlalchemy.sql.schema.Index"], "uses_pep604_syntax": false}, "values": [], "variance": 0}]}}}, "_is_uq": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_classvar", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl._autogen._uq_constraint_sig._is_uq", "name": "_is_uq", "type": "builtins.bool"}}, "_register": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["cls"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "alembic.ddl._autogen._uq_constraint_sig._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl._autogen._uq_constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _uq_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._uq_constraint_sig._register", "name": "_register", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["cls"], "arg_types": [{".class": "TypeType", "item": "alembic.ddl._autogen._uq_constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_register of _uq_constraint_sig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "column_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "alembic.ddl._autogen._uq_constraint_sig.column_names", "name": "column_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.ddl._autogen._uq_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names of _uq_constraint_sig", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "alembic.ddl._autogen._uq_constraint_sig.column_names", "name": "column_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["alembic.ddl._autogen._uq_constraint_sig"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "column_names of _uq_constraint_sig", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_unique": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "alembic.ddl._autogen._uq_constraint_sig.is_unique", "name": "is_unique", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "alembic.ddl._autogen._uq_constraint_sig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "alembic.ddl._autogen._uq_constraint_sig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "is_fk_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen.is_fk_sig", "name": "is_fk_sig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sig"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_fk_sig", "ret_type": "builtins.bool", "type_guard": "alembic.ddl._autogen._fk_constraint_sig", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_index_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen.is_index_sig", "name": "is_index_sig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sig"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_index_sig", "ret_type": "builtins.bool", "type_guard": "alembic.ddl._autogen._ix_constraint_sig", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_uq_sig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "alembic.ddl._autogen.is_uq_sig", "name": "is_uq_sig", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sig"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "alembic.ddl._autogen._constraint_sig"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_uq_sig", "ret_type": "builtins.bool", "type_guard": "alembic.ddl._autogen._uq_constraint_sig", "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sqla_compat": {".class": "SymbolTableNode", "cross_ref": "alembic.util.sqla_compat", "kind": "Gdef"}, "util": {".class": "SymbolTableNode", "cross_ref": "alembic.util", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\ddl\\_autogen.py"}