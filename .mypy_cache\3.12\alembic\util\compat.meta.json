{"data_mtime": 1753035428, "dep_lines": [23, 20, 46, 49, 3, 5, 6, 7, 8, 9, 10, 46, 54, 59, 61, 1, 1, 1, 1, 1, 1, 55], "dep_prios": [5, 5, 10, 5, 5, 5, 10, 10, 5, 10, 5, 20, 10, 10, 10, 5, 30, 30, 30, 30, 30, 5], "dependencies": ["sqlalchemy.util.compat", "sqlalchemy.util", "importlib.resources", "importlib.metadata", "__future__", "configparser", "io", "os", "pathlib", "sys", "typing", "importlib", "importlib_resources", "<PERSON><PERSON><PERSON><PERSON>", "to<PERSON>li", "builtins", "_frozen_importlib", "_io", "_typeshed", "abc", "types"], "hash": "5b444ce24e28ce7e14d36075f60c2345a7472a5e", "id": "alembic.util.compat", "ignore_all": true, "interface_hash": "c80b12fb72376d5c2758e74ab7643be5f72a9ce4", "mtime": 1750655083, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\alembic\\util\\compat.py", "plugin_data": null, "size": 4248, "suppressed": ["importlib_metadata"], "version_id": "1.15.0"}