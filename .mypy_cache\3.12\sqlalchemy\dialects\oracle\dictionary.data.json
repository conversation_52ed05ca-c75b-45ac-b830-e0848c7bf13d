{".class": "MypyFile", "_fullname": "sqlalchemy.dialects.oracle.dictionary", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CHAR": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.sqltypes.CHAR", "kind": "Gdef"}, "Column": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Column", "kind": "Gdef"}, "DATE": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.DATE", "kind": "Gdef"}, "DB_LINK_PLACEHOLDER": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.DB_LINK_PLACEHOLDER", "name": "DB_LINK_PLACEHOLDER", "type": "builtins.str"}}, "LONG": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.LONG", "kind": "Gdef"}, "MetaData": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.MetaData", "kind": "Gdef"}, "NUMBER": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.NUMBER", "kind": "Gdef"}, "RAW": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.RAW", "kind": "Gdef"}, "Table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql.schema.Table", "kind": "Gdef"}, "VARCHAR2": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.dialects.oracle.types.VARCHAR2", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.dictionary.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.dictionary.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.dictionary.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.dictionary.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.dictionary.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "sqlalchemy.dialects.oracle.dictionary.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "all_col_comments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_col_comments", "name": "all_col_comments", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_cons_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_cons_columns", "name": "all_cons_columns", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_constraints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_constraints", "name": "all_constraints", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_db_links": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_db_links", "name": "all_db_links", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_ind_columns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_ind_columns", "name": "all_ind_columns", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_ind_expressions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_ind_expressions", "name": "all_ind_expressions", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_indexes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_indexes", "name": "all_indexes", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_mview_comments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_mview_comments", "name": "all_mview_comments", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_mviews": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_mviews", "name": "all_mviews", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_objects": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_objects", "name": "all_objects", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_sequences": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_sequences", "name": "all_sequences", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_synonyms": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_synonyms", "name": "all_synonyms", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_tab_cols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_tab_cols", "name": "all_tab_cols", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_tab_comments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_tab_comments", "name": "all_tab_comments", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_tab_identity_cols": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_tab_identity_cols", "name": "all_tab_identity_cols", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_tables": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_tables", "name": "all_tables", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_users": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_users", "name": "all_users", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "all_views": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.all_views", "name": "all_views", "type": "sqlalchemy.sql.selectable.NamedFromClause"}}, "dictionary_meta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.dictionary_meta", "name": "dictionary_meta", "type": "sqlalchemy.sql.schema.MetaData"}}, "dual": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "sqlalchemy.dialects.oracle.dictionary.dual", "name": "dual", "type": "sqlalchemy.sql.selectable.TableClause"}}, "table": {".class": "SymbolTableNode", "cross_ref": "sqlalchemy.sql._selectable_constructors.table", "kind": "Gdef"}}, "path": "e:\\admin_panel\\admin_backend\\venv\\Lib\\site-packages\\sqlalchemy\\dialects\\oracle\\dictionary.py"}