{".class": "MypyFile", "_fullname": "app.api.upload", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Namespace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.upload.Namespace", "name": "Namespace", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": null, "type_of_any": 3}}}, "Resource": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.upload.Resource", "name": "Resource", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.Resource", "source_any": null, "type_of_any": 3}}}, "UploadImage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "app.api.upload.UploadImage", "name": "UploadImage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "app.api.upload.UploadImage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "app.api.upload", "mro": ["app.api.upload.UploadImage", "builtins.object"], "names": {".class": "SymbolTable", "post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "app.api.upload.UploadImage.post", "name": "post", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "app.api.upload.UploadImage.post", "name": "post", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "app.api.upload.UploadImage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "app.api.upload.UploadImage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.upload.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.upload.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.upload.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.upload.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.upload.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "app.api.upload.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "error_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.upload.error_model", "name": "error_model", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "fields": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.upload.fields", "name": "fields", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.fields", "source_any": null, "type_of_any": 3}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "reqparse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "app.api.upload.reqparse", "name": "reqparse", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.reqparse", "source_any": null, "type_of_any": 3}}}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "secure_filename": {".class": "SymbolTableNode", "cross_ref": "werkzeug.utils.secure_filename", "kind": "Gdef"}, "token_required": {".class": "SymbolTableNode", "cross_ref": "app.utils.decorators.token_required", "kind": "Gdef"}, "upload_ns": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.upload.upload_ns", "name": "upload_ns", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "upload_parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.upload.upload_parser", "name": "upload_parser", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.reqparse", "source_any": {".class": "AnyType", "missing_import_name": "app.api.upload.reqparse", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}, "upload_response_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "app.api.upload.upload_response_model", "name": "upload_response_model", "type": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": {".class": "AnyType", "missing_import_name": "app.api.upload.Namespace", "source_any": null, "type_of_any": 3}, "type_of_any": 7}}}}, "path": "E:\\admin_panel\\admin_backend\\app\\api\\upload.py"}